/**
 * Enhanced Single Auction Page Styles
 *
 * @package AuctionSystem
 * @version 2.0.0
 * @description تصميم محسن لصفحة المزاد المفرد مع ألوان الموقع الأصلي
 */

/* ========================================
   VARIABLES & COLORS
   ======================================== */
:root {
    /* الألوان الأساسية من الموقع الأصلي */
    --primary-red: #dc2626;
    --primary-dark: #1e293b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-light: #e5e7eb;
    --background: #ffffff;
    --background-light: #f9fafb;
    --background-gray: #f8f9fa;

    /* ألوان إضافية */
    --success: #059669;
    --warning: #d97706;
    --danger: #dc2626;

    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* الحدود */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* الظلال */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

    /* الانتقالات */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ========================================
   COUNTDOWN TIMER
   ======================================== */
.auction-countdown {
    margin: 1.5rem 0;
    padding: 1rem;
    background: var(--background);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
}

.countdown-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.countdown-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 4.5rem;
    padding: 0.75rem 0.5rem;
    background: var(--background-light);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
}

.countdown-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-red);
    line-height: 1.2;
    font-family: 'Tajawal', sans-serif;
}

.countdown-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
}

.countdown-separator {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-secondary);
    margin: 0 0.25rem;
    align-self: flex-end;
    padding-bottom: 1rem;
}

.countdown-expired {
    background-color: #fef2f2;
    color: var(--danger);
    padding: 0.75rem 1.25rem;
    border-radius: var(--radius-md);
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
}

.countdown-expired .expired-icon {
    font-size: 1.2em;
}

@media (max-width: 768px) {
    .countdown-item {
        min-width: 3.5rem;
        padding: 0.5rem 0.25rem;
    }
    
    .countdown-number {
        font-size: 1.25rem;
    }
    
    .countdown-label {
        font-size: 0.65rem;
    }
    
    .countdown-separator {
        font-size: 1.2rem;
        padding-bottom: 0.8rem;
    }
}

@media (max-width: 480px) {
    .countdown-item {
        min-width: 3rem;
        padding: 0.4rem 0.2rem;
    }
    
    .countdown-number {
        font-size: 1.1rem;
    }
    
    .countdown-label {
        font-size: 0.6rem;
    }
    
    .countdown-separator {
        font-size: 1rem;
        padding-bottom: 0.7rem;
    }
}

/* ========================================
   BASE STYLES
   ======================================== */
.single-auction-page {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
    width: 100%;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* ========================================
   ENHANCED HEADER SECTION
   ======================================== */
.auction-header {
    background: var(--background);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
}

.auction-meta-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
}

.auction-breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.auction-breadcrumb a {
    color: var(--primary-red);
    text-decoration: none;
    transition: var(--transition-fast);
}

.auction-breadcrumb a:hover {
    color: var(--primary-dark);
}

.auction-breadcrumb .separator {
    color: var(--text-secondary);
    margin: 0 var(--spacing-xs);
}

.auction-breadcrumb .current {
    color: var(--text-primary);
    font-weight: 600;
}

.auction-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.action-btn {
    background: var(--background-light);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: var(--primary-red);
    color: var(--background);
    border-color: var(--primary-red);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* ========================================
   TITLE SECTION
   ======================================== */
.auction-title-section {
    margin-bottom: var(--spacing-xl);
}

.auction-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.2;
}

.auction-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    align-items: center;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.meta-item svg {
    color: var(--primary-red);
    flex-shrink: 0;
}

.meta-item.auction-type .type-badge {
    background: linear-gradient(135deg, var(--primary-red), #b91c1c);
    color: var(--background);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.location-link-header {
    color: var(--primary-red);
    text-decoration: none;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.location-link-header:hover {
    color: var(--primary-dark);
}

/* Brochure Meta Item */
.brochure-meta .brochure-link {
    color: var(--primary-red);
    text-decoration: none;
    transition: var(--transition-fast);
    font-weight: 500;
}

.brochure-meta .brochure-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.brochure-meta svg {
    margin-left: 4px;
    vertical-align: middle;
}

/* ========================================
   SOCIAL SHARE BUTTONS
   ======================================== */
.auction-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.social-share {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.share-btn {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-xs);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    width: 36px;
    height: 36px;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Platform-specific colors */
.whatsapp-share:hover {
    background: #25D366;
    color: white;
    border-color: #25D366;
}

.twitter-share:hover {
    background: #1DA1F2;
    color: white;
    border-color: #1DA1F2;
}

.facebook-share:hover {
    background: #1877F2;
    color: white;
    border-color: #1877F2;
}

.telegram-share:hover {
    background: #0088CC;
    color: white;
    border-color: #0088CC;
}

.copy-link:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Share button animations */
@keyframes shareButtonSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.share-btn-animate {
    animation: shareButtonSlideIn 0.3s ease forwards;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .social-share {
        gap: var(--spacing-xs);
    }

    .share-btn {
        width: 32px;
        height: 32px;
        padding: 6px;
    }

    .share-btn svg {
        width: 16px;
        height: 16px;
    }
}

/* ========================================
   LOCATION PREVIEW (NO API REQUIRED)
   ======================================== */
.auction-location-section {
    margin: var(--spacing-lg) 0;
}

.auction-location-section h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.location-map-container {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.map-wrapper {
    position: relative;
    width: 100%;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    cursor: pointer;
    transition: var(--transition-fast);
}

.map-wrapper:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.map-preview-container {
    padding: var(--spacing-xl);
    text-align: center;
}

.map-preview-content {
    margin-bottom: var(--spacing-lg);
}

.location-icon {
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    animation: locationPulse 2s ease-in-out infinite;
}

@keyframes locationPulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.8; }
}

.location-details h4 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.city-name {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 500;
}

.map-instruction {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.875rem;
    font-style: italic;
}

/* Map Action Buttons */
.map-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    flex-wrap: wrap;
}

.map-action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition-fast);
    border: none;
    cursor: pointer;
    white-space: nowrap;
}

.map-action-btn.primary-btn {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 2px 4px rgba(0, 123, 186, 0.2);
}

.map-action-btn.primary-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 186, 0.3);
}

.map-action-btn.secondary-btn {
    background: var(--background-color);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.map-action-btn.secondary-btn:hover {
    background: var(--surface-color);
    color: var(--text-primary);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.map-action-btn svg {
    flex-shrink: 0;
}

/* Map Preview Image (if available) */
.map-preview-image {
    width: 100%;
    height: 200px;
    background: var(--background-light);
    border-radius: var(--border-radius);
    margin-top: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
}

.map-preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--border-radius);
}

/* Mobile responsive */
@media (max-width: 768px) {
    .map-preview-container {
        padding: var(--spacing-lg);
    }

    .location-icon svg {
        width: 48px;
        height: 48px;
    }

    .map-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .map-action-btn {
        justify-content: center;
        padding: var(--spacing-md);
    }

    .location-details h4 {
        font-size: 1rem;
    }

    .city-name {
        font-size: 0.9rem;
    }

    .map-instruction {
        font-size: 0.8rem;
    }
}

/* Tablet responsive */
@media (max-width: 1024px) and (min-width: 769px) {
    .map-actions {
        gap: var(--spacing-xs);
    }

    .map-action-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
    }
}

/* ========================================
   ENHANCED COUNTDOWN TIMER
   ======================================== */
.auction-countdown {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    color: var(--background);
    text-align: center;
    box-shadow: 0 8px 25px rgba(44, 62, 80, 0.2);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.auction-countdown::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
    pointer-events: none;
}

.countdown-container {
    position: relative;
    z-index: 1;
}

.countdown-container h3 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: #ecf0f1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.countdown {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.countdown-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.2rem 0.8rem;
    min-width: 85px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.countdown-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.countdown-number {
    display: block;
    font-size: 2.2rem;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 0.5rem;
    color: #3498db;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    font-family: 'Arial', sans-serif;
}

.countdown-label {
    font-size: 0.85rem;
    opacity: 0.9;
    color: #bdc3c7;
    font-weight: 500;
}

.countdown-expired {
    padding: 2rem;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.expired-icon {
    font-size: 2.5rem;
    display: block;
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.expired-text {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ecf0f1;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Countdown Animation */
@keyframes countdownPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.countdown-number.updating {
    animation: countdownPulse 0.5s ease;
}

/* ========================================
   LAYOUT GRID
   ======================================== */
.auction-content {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.main-content {
    background: var(--background);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
}

.sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* ========================================
   AUCTION DESCRIPTION SECTION
   ======================================== */
.auction-description-section {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-xl);
    border-bottom: 1px solid var(--border-light);
}

/* Toggle Button */
.description-toggle {
    width: 100%;
    background: var(--background-light);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    display: block;
    text-align: right;
}

.description-toggle:hover {
    background: var(--background);
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.1);
}

.toggle-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.toggle-icon-text {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.toggle-icon-text svg {
    color: var(--primary-red);
    flex-shrink: 0;
}

.toggle-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.toggle-arrow {
    color: var(--text-secondary);
    transition: transform 0.3s ease;
    flex-shrink: 0;
}

.description-toggle[aria-expanded="true"] .toggle-arrow {
    transform: rotate(180deg);
}

.description-toggle[aria-expanded="true"] {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-color: transparent;
}

/* Content */
.description-content {
    background: var(--background);
    border: 1px solid var(--border-light);
    border-top: none;
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    overflow: hidden;
    transition: all 0.3s ease;
}

.description-content.show {
    display: block !important;
}

.description-text {
    padding: var(--spacing-xl);
    border-right: 4px solid var(--primary-red);
}

.description-text p {
    color: var(--text-secondary);
    line-height: 1.7;
    font-size: 1rem;
    margin-bottom: var(--spacing-md);
}

.description-text p:last-child {
    margin-bottom: 0;
}

/* Location styles completely removed */

/* ========================================
   ADVERTISEMENT SPACES
   ======================================== */
.ad-space {
    text-align: center;
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: var(--transition-normal);
}

.ad-space:empty {
    display: none;
}

.ad-top-banner {

    background: linear-gradient(135deg, var(--background-gray) 0%, #e9ecef 100%);
    border: 1px solid var(--border-light);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    border-radius: var(--radius-lg);
}

.ad-content-middle {
    background: var(--background-light);
    border: 2px dashed var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin: var(--spacing-xl) 0;
}

.ad-bottom-banner {
    background: linear-gradient(135deg, var(--primary-red) 0%, #b91c1c 100%);
    color: var(--background);
    padding: var(--spacing-xl);
    margin-top: var(--spacing-xl);
    box-shadow: 0 4px 20px rgba(220, 38, 38, 0.2);
    border-radius: var(--radius-lg);
}

.ad-sidebar-top,
.ad-sidebar-bottom {
    background: var(--background);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
    box-shadow: var(--shadow-md);
}

.ad-space img {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
}

.ad-space img:hover {
    transform: scale(1.02);
}

/* ========================================
   ASSETS SECTION
   ======================================== */
.auction-assets h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.auction-assets h3::before {
    content: '';
    width: 4px;
    height: 24px;
    background: var(--primary-red);
    border-radius: var(--radius-sm);
}

.assets-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* ========================================
   ENHANCED ASSET CARDS
   ======================================== */
.asset-card {
    background: var(--background);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    opacity: 0;
    transform: translateY(30px);
}

.asset-card.loaded {
    opacity: 1;
    transform: translateY(0);
}

.asset-card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
}

.asset-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
}

.asset-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.asset-type {
    background: linear-gradient(135deg, var(--primary-red), #b91c1c);
    color: var(--background);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ========================================
   ENHANCED GALLERY
   ======================================== */
.asset-gallery {
    margin-bottom: var(--spacing-lg);
}

.gallery-container {
    display: grid;
    grid-template-columns: 1fr 120px;
    gap: var(--spacing-lg);
    height: 300px;
}

.main-image-container {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--background-gray);
}

.main-image-display {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-normal);
}

.main-image-display:hover {
    transform: scale(1.05);
}

.gallery-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.5);
    color: var(--background);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
    backdrop-filter: blur(10px);
}

.gallery-nav:hover {
    background: rgba(0, 0, 0, 0.7);
    transform: translateY(-50%) scale(1.1);
}

.gallery-prev {
    right: var(--spacing-md);
}

.gallery-next {
    left: var(--spacing-md);
}

.thumbnails-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    overflow-y: auto;
    padding: var(--spacing-xs);
}

.thumbnail-wrapper {
    border-radius: var(--radius-md);
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition-fast);
    border: 2px solid transparent;
}

.thumbnail-wrapper.active {
    border-color: var(--primary-red);
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
}

.thumbnail-wrapper:hover {
    transform: scale(1.05);
}

.thumbnail-image {
    width: 100%;
    height: 80px;
    object-fit: cover;
    display: block;
}

/* ========================================
   ASSET DETAILS
   ======================================== */
.asset-details {
    margin-bottom: var(--spacing-lg);
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.detail-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.detail-value {
    font-size: 1rem;
    color: var(--text-primary);
    font-weight: 600;
}

.asset-description {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--background-light);
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--primary-red);
}

.asset-description p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

.asset-location {
    margin-top: var(--spacing-md);
}

.location-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--primary-red);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--primary-red);
    border-radius: var(--radius-md);
    background: rgba(220, 38, 38, 0.05);
}

.location-link:hover {
    background: var(--primary-red);
    color: var(--background);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* ========================================
   LAZY LOADING SYSTEM
   ======================================== */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    background: var(--background-light);
    border-radius: var(--radius-lg);
    border: 2px dashed var(--border-light);
    margin: var(--spacing-xl) 0;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-red);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-lg);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.load-more-trigger {
    text-align: center;
    padding: var(--spacing-xl);
    margin: var(--spacing-xl) 0;
}

.load-more-button {
    background: linear-gradient(135deg, var(--primary-red) 0%, #b91c1c 100%);
    color: var(--background);
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-xl);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.load-more-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
}

.load-more-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* ========================================
   SIDEBAR COMPONENTS
   ======================================== */
.info-card {
    background: var(--background);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.info-card:hover {
    box-shadow: var(--shadow-lg);
}

.info-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.info-card h3::before {
    content: '';
    width: 4px;
    height: 20px;
    background: var(--primary-red);
    border-radius: var(--radius-sm);
}

/* Company Information */
.company-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.detail-row {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
}

.detail-row:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.detail-row strong {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.detail-row span {
    font-size: 1rem;
    color: var(--text-primary);
    font-weight: 600;
}

.detail-row a {
    color: var(--primary-red);
    text-decoration: none;
    transition: var(--transition-fast);
}

.detail-row a:hover {
    color: var(--primary-dark);
}

/* Contact Actions */
.contact-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.contact-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-normal);
    border: 2px solid;
}

.phone-btn {
    background: var(--success);
    color: var(--background);
    border-color: var(--success);
}

.phone-btn:hover {
    background: #047857;
    border-color: #047857;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.email-btn {
    background: transparent;
    color: var(--primary-red);
    border-color: var(--primary-red);
}

.email-btn:hover {
    background: var(--primary-red);
    color: var(--background);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* ========================================
   BROCHURE SECTION
   ======================================== */
.auction-brochure {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-xl);
    border-bottom: 1px solid var(--border-light);
}

.auction-brochure h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.auction-brochure h3::before {
    content: '';
    width: 4px;
    height: 24px;
    background: var(--primary-red);
    border-radius: var(--radius-sm);
}

.brochure-card {
    background: var(--background-light);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: var(--transition-normal);
}

.brochure-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.brochure-icon {
    color: var(--primary-red);
    flex-shrink: 0;
}

.brochure-info h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.brochure-info p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.5;
}

.download-brochure {
    background: var(--primary-red);
    color: var(--background);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.download-brochure:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

/* Tablet */
@media (max-width: 1024px) {
    .auction-content {
        grid-template-columns: 1fr 300px;
        gap: var(--spacing-lg);
    }

    .auction-title {
        font-size: 2rem;
    }

    .gallery-container {
        grid-template-columns: 1fr 100px;
        height: 250px;
    }

    .thumbnail-image {
        height: 60px;
    }
}

/* Mobile */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .single-auction-page {
        padding: var(--spacing-md) 0;
    }

    .auction-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .auction-header {
        padding: var(--spacing-lg);
    }

    .auction-meta-top {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }

    .auction-breadcrumb {
        font-size: 0.75rem;
    }

    .auction-title {
        font-size: 1.75rem;
        line-height: 1.3;
    }

    .auction-meta {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }

    .countdown {
        gap: var(--spacing-md);
    }

    .countdown-item {
        min-width: 60px;
        padding: var(--spacing-md);
    }

    .countdown-number {
        font-size: 1.5rem;
    }

    .main-content {
        padding: var(--spacing-lg);
    }

    /* Location styles removed from mobile */

    /* Description Mobile Styles */
    .description-toggle {
        padding: var(--spacing-md);
    }

    .toggle-title {
        font-size: 1.25rem;
    }

    .toggle-arrow {
        width: 18px;
        height: 18px;
    }

    .description-text {
        padding: var(--spacing-md);
    }

    .gallery-container {
        grid-template-columns: 1fr;
        height: auto;
        gap: var(--spacing-md);
    }

    .main-image-container {
        height: 250px;
    }

    .thumbnails-container {
        flex-direction: row;
        overflow-x: auto;
        padding: var(--spacing-sm);
    }

    .thumbnail-wrapper {
        flex-shrink: 0;
        width: 80px;
    }

    .thumbnail-image {
        height: 60px;
        width: 80px;
    }

    .details-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .brochure-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .contact-actions {
        gap: var(--spacing-sm);
    }

    .load-more-button {
        width: 100%;
        justify-content: center;
    }

    /* Hide sidebar ads on small screens */
    .ad-sidebar-top,
    .ad-sidebar-bottom {
        display: none;
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .auction-header {
        padding: var(--spacing-md);
    }

    .auction-title {
        font-size: 1.5rem;
    }

    .main-content {
        padding: var(--spacing-md);
    }

    .info-card {
        padding: var(--spacing-md);
    }

    .countdown-item {
        min-width: 50px;
        padding: var(--spacing-sm);
    }

    .countdown-number {
        font-size: 1.25rem;
    }

    .countdown-label {
        font-size: 0.75rem;
    }

    .main-image-container {
        height: 200px;
    }

    .thumbnail-wrapper {
        width: 60px;
    }

    .thumbnail-image {
        height: 45px;
        width: 60px;
    }
}

/* ========================================
   PRINT STYLES
   ======================================== */
@media print {
    .single-auction-page {
        background: white;
        padding: 0;
    }

    .auction-actions,
    .ad-space,
    .contact-actions,
    .load-more-button {
        display: none !important;
    }

    .auction-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .auction-header,
    .main-content,
    .info-card {
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .auction-countdown {
        background: #f5f5f5;
        color: #333;
    }
}

/* ========================================
   ACCESSIBILITY IMPROVEMENTS
   ======================================== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
.action-btn:focus,
.contact-btn:focus,
.load-more-button:focus,
.location-link:focus,
.download-brochure:focus {
    outline: 2px solid var(--primary-red);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-light: #000000;
        --text-secondary: #000000;
        --background-light: #ffffff;
    }
}
