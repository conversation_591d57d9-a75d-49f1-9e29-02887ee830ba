/**
 * Single Auction Page - User Interactions
 *
 * @package AuctionSystem
 * @version 1.0.0
 * @description إدارة التفاعلات مع المستخدم (نسخ، طباعة، مشاركة، تمرير)
 */

(function($) {
    'use strict';

    /**
     * Bind additional events
     */
    function bindEvents() {
        // Smooth scroll for anchor links
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            const target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 500);
            }
        });

        // Copy to clipboard functionality
        $('.copy-link').on('click', function(e) {
            e.preventDefault();
            const textToCopy = $(this).data('copy') || window.location.href;

            if (navigator.clipboard) {
                navigator.clipboard.writeText(textToCopy).then(() => {
                    if (window.AuctionNotifications) {
                        window.AuctionNotifications.showToast('تم نسخ الرابط');
                    }
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = textToCopy;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                if (window.AuctionNotifications) {
                    window.AuctionNotifications.showToast('تم نسخ الرابط');
                }
            }
        });

        // Print functionality
        $('.print-auction').on('click', function(e) {
            e.preventDefault();
            window.print();
        });

        // Share functionality
        $('.share-auction').on('click', function(e) {
            e.preventDefault();

            if (navigator.share) {
                navigator.share({
                    title: document.title,
                    text: 'شاهد هذا المزاد',
                    url: window.location.href
                });
            } else {
                // Fallback - copy to clipboard
                $('.copy-link').trigger('click');
            }
        });

        // Image zoom functionality
        $('.gallery-item img').on('click', function() {
            const imgSrc = $(this).attr('src');
            const imgAlt = $(this).attr('alt');

            if (window.AuctionGallery) {
                window.AuctionGallery.showImageModal(imgSrc, imgAlt);
            }
        });

        // Contact company functionality
        $('.contact-company').on('click', function(e) {
            e.preventDefault();
            // This could open a modal or redirect to contact form
            if (window.AuctionNotifications) {
                window.AuctionNotifications.showToast('يمكنك التواصل مع الشركة باستخدام معلومات الاتصال المعروضة');
            }
        });
    }

    // Make functions globally available
    window.AuctionInteractions = {
        bindEvents: bindEvents
    };



})(jQuery);
