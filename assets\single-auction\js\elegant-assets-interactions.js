/**
 * Elegant Assets Interactions
 *
 * @package AuctionSystem
 * @version 2.0.0
 * @description تفاعلات أنيقة للأصول
 */

(function($) {
    'use strict';

    /**
     * Initialize elegant assets interactions
     */
    function initElegantAssetsInteractions() {
        // console.log('Initializing Elegant Assets Interactions...');

        // Initialize all interaction components

        // Initialize horizontal gallery interactions
        initHorizontalGalleryInteractions();

        // Initialize gallery interactions (legacy)
        initElegantGalleryInteractions();

        // Initialize asset card animations
        initAssetCardAnimations();

        // Initialize action buttons
        initActionButtons();

        // Initialize collapsible descriptions
        initCollapsibleDescriptions();

        // Initialize responsive behaviors
        initResponsiveBehaviors();

        // console.log('Elegant Assets Interactions initialized successfully!');
    }

    /**
     * Initialize horizontal gallery with auto slideshow
     */
    function initHorizontalGalleryInteractions() {
        // Helper: تحديث حالة الجاليري
        function updateGallery($gallery, newIndex) {
            const $images = $gallery.find('.slideshow-image');
            const $dots = $gallery.find('.slideshow-dot');
            const $counter = $gallery.find('.current-image');
            const totalImages = $images.length;
            $images.removeClass('active');
            $dots.removeClass('active');
            $images.eq(newIndex).addClass('active');
            $dots.eq(newIndex).addClass('active');
            $counter.text(newIndex + 1);
            $gallery.data('currentIndex', newIndex);
        }

        // Helper: بدء السلايد شو
        function startAutoSlide($gallery) {
            stopAutoSlide($gallery);
            const totalImages = $gallery.find('.slideshow-image').length;
            if (totalImages <= 1) return;
            const interval = setInterval(function() {
                let currentIndex = $gallery.data('currentIndex') || 0;
                currentIndex = (currentIndex + 1) % totalImages;
                updateGallery($gallery, currentIndex);
            }, 4000);
            $gallery.data('autoSlideInterval', interval);
        }

        // Helper: إيقاف السلايد شو
        function stopAutoSlide($gallery) {
            const interval = $gallery.data('autoSlideInterval');
            if (interval) {
                clearInterval(interval);
                $gallery.removeData('autoSlideInterval');
            }
        }

        // تهيئة كل جاليري عند تحميل الصفحة أو إضافته ديناميكياً
        $('.horizontal-asset-gallery').each(function() {
            const $gallery = $(this);
            if ($gallery.data('initialized')) return;
            $gallery.data('initialized', true);
            $gallery.data('currentIndex', 0);
            startAutoSlide($gallery);
        });

        // Delegation: النقاط
        $(document).on('click', '.slideshow-dot', function() {
            const $dot = $(this);
            const $gallery = $dot.closest('.horizontal-asset-gallery');
            const targetIndex = $dot.data('image-index');
            updateGallery($gallery, targetIndex);
            stopAutoSlide($gallery);
            startAutoSlide($gallery);
        });

        // Delegation: زر fullscreen
        $(document).on('click', '.simple-fullscreen-btn', function() {
            const $btn = $(this);
            const $gallery = $btn.closest('.horizontal-asset-gallery');
            const $images = $gallery.find('.slideshow-image');
            let currentIndex = $gallery.data('currentIndex') || 0;
            const currentImageSrc = $images.eq(currentIndex).attr('src');
            if (currentImageSrc) {
                openSimpleFullscreen(currentImageSrc);
            }
        });

        // Delegation: hover لإيقاف/تشغيل السلايد شو
        $(document).on('mouseenter', '.horizontal-asset-gallery', function() {
            stopAutoSlide($(this));
        });
        $(document).on('mouseleave', '.horizontal-asset-gallery', function() {
            startAutoSlide($(this));
        });
    }

    /**
     * Initialize elegant gallery interactions
     */
    function initElegantGalleryInteractions() {
        // Thumbnail navigation (delegated)
        $(document).on('click', '.elegant-thumb-item', function() {
            const $this = $(this);
            const imageIndex = $this.data('image-index');
            const $gallery = $this.closest('.elegant-asset-gallery');
            const $mainImage = $gallery.find('.elegant-main-image');
            const newSrc = $this.find('.elegant-thumb-image').attr('src');

            // Update active thumbnail
            $gallery.find('.elegant-thumb-item').removeClass('active');
            $this.addClass('active');

            // Update main image with elegant transition
            $mainImage.fadeOut(300, function() {
                $mainImage.attr('src', newSrc).attr('data-image-index', imageIndex);
                $mainImage.fadeIn(300);
            });

            // Update image indicator
            const $indicator = $gallery.find('.elegant-current-num');
            $indicator.text(imageIndex + 1);

            // Add pulse animation to indicator
            $indicator.addClass('updating');
            setTimeout(() => $indicator.removeClass('updating'), 500);
        });

        // Gallery navigation arrows (delegated)
        $(document).on('click', '.elegant-nav-btn', function() {
            const $this = $(this);
            const $gallery = $this.closest('.elegant-asset-gallery');
            const $mainImage = $gallery.find('.elegant-main-image');
            const currentIndex = parseInt($mainImage.attr('data-image-index')) || 0;
            const $thumbnails = $gallery.find('.elegant-thumb-item');
            const totalImages = $thumbnails.length;

            let newIndex;
            if ($this.hasClass('prev-btn')) {
                newIndex = currentIndex > 0 ? currentIndex - 1 : totalImages - 1;
            } else {
                newIndex = currentIndex < totalImages - 1 ? currentIndex + 1 : 0;
            }

            $thumbnails.eq(newIndex).trigger('click');
        });

        // Fullscreen button
        $('.elegant-fullscreen-btn').on('click', function() {
            const $gallery = $(this).closest('.elegant-asset-gallery');
            const $mainImage = $gallery.find('.elegant-main-image');
            const imageSrc = $mainImage.attr('src');

            openImageFullscreen(imageSrc);
        });

        // Keyboard navigation
        $(document).on('keydown', function(e) {
            const $activeGallery = $('.elegant-asset-gallery:hover');
            if ($activeGallery.length === 0) return;

            if (e.key === 'ArrowLeft') {
                e.preventDefault();
                $activeGallery.find('.prev-btn').trigger('click');
            } else if (e.key === 'ArrowRight') {
                e.preventDefault();
                $activeGallery.find('.next-btn').trigger('click');
            } else if (e.key === 'Escape') {
                closeImageFullscreen();
            }
        });
    }

    /**
     * Initialize asset card animations
     */
    function initAssetCardAnimations() {
        // Intersection Observer for scroll animations
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const $card = $(entry.target);
                        $card.addClass('animate-in');
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });

            $('.elegant-asset-card').each(function(index) {
                const $card = $(this);
                // تأخير أطول للتخطيط الجديد (صف واحد لكل أصل)
                $card.css('animation-delay', (index * 0.2) + 's');
                observer.observe(this);
            });
        } else {
            // Fallback for older browsers
            $('.elegant-asset-card').addClass('animate-in');
        }

        // Hover effects
        $('.elegant-asset-card').on('mouseenter', function() {
            $(this).addClass('hovered');
        }).on('mouseleave', function() {
            $(this).removeClass('hovered');
        });

        // Info card hover effects
        $('.elegant-info-card').on('mouseenter', function() {
            $(this).find('.elegant-card-icon').addClass('bounce');
        }).on('mouseleave', function() {
            $(this).find('.elegant-card-icon').removeClass('bounce');
        });
    }

    /**
     * Initialize collapsible descriptions
     */
    function initCollapsibleDescriptions() {
        // Collapsible descriptions (delegated)
        $(document).on('click', '.elegant-description-toggle', function() {
            const $toggle = $(this);
            const $content = $toggle.siblings('.elegant-description-content');
            const isExpanded = $toggle.attr('aria-expanded') === 'true';

            if (isExpanded) {
                // Collapse
                $content.slideUp(300, function() {
                    $content.removeClass('expanded');
                });
                $toggle.attr('aria-expanded', 'false');
            } else {
                // Expand
                $content.addClass('expanded').slideDown(300);
                $toggle.attr('aria-expanded', 'true');
            }
        });

        // Keyboard accessibility (delegated)
        $(document).on('keydown', '.elegant-description-toggle', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                $(this).trigger('click');
            }
        });
    }

    /**
     * Initialize action buttons
     */
    function initActionButtons() {
        // Share button functionality
        $('.elegant-share-btn').on('click', function() {
            const assetTitle = $(this).data('asset-title');
            const currentUrl = window.location.href;

            if (navigator.share) {
                navigator.share({
                    title: assetTitle,
                    url: currentUrl
                }).catch(console.error);
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(currentUrl).then(() => {
                    showElegantNotification('تم نسخ الرابط إلى الحافظة', 'success');
                }).catch(() => {
                    showElegantNotification('فشل في نسخ الرابط', 'error');
                });
            }
        });

        // Map button hover effect
        $('.elegant-map-btn').on('mouseenter', function() {
            $(this).find('svg').addClass('bounce');
        }).on('mouseleave', function() {
            $(this).find('svg').removeClass('bounce');
        });

        // Button ripple effect
        $('.elegant-action-btn').on('click', function(e) {
            const $button = $(this);
            const rect = this.getBoundingClientRect();
            const ripple = $('<span class="ripple"></span>');

            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.css({
                width: size,
                height: size,
                left: x,
                top: y
            });

            $button.append(ripple);

            setTimeout(() => ripple.remove(), 600);
        });
    }

    /**
     * Initialize responsive behaviors
     */
    function initResponsiveBehaviors() {
        let resizeTimer;

        $(window).on('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(function() {
                adjustGalleryLayout();
                adjustCardSpacing();
            }, 250);
        });

        // Initial layout adjustment
        adjustGalleryLayout();
        adjustCardSpacing();
    }

    /**
     * Adjust gallery layout based on screen size
     */
    function adjustGalleryLayout() {
        const screenWidth = $(window).width();

        $('.elegant-asset-gallery').each(function() {
            const $gallery = $(this);

            if (screenWidth <= 768) {
                $gallery.addClass('mobile-layout');
            } else {
                $gallery.removeClass('mobile-layout');
            }
        });
    }

    /**
     * Adjust card spacing for single row layout
     */
    function adjustCardSpacing() {
        const screenWidth = $(window).width();
        const $grid = $('.elegant-assets-grid');

        if (screenWidth <= 480) {
            $grid.addClass('compact-spacing');
        } else {
            $grid.removeClass('compact-spacing');
        }

        // تعديل إضافي للتخطيط الجديد
        $('.elegant-asset-card').each(function(index) {
            const $card = $(this);
            if (screenWidth <= 768) {
                $card.addClass('mobile-optimized');
            } else {
                $card.removeClass('mobile-optimized');
            }
        });
    }

    /**
     * Open image in simple fullscreen
     */
    function openSimpleFullscreen(imageSrc) {
        const $overlay = $(`
            <div class="simple-fullscreen-overlay">
                <div class="simple-fullscreen-content">
                    <img src="${imageSrc}" alt="صورة بالحجم الكامل" class="simple-fullscreen-image">
                    <button class="simple-close-btn" title="إغلاق">
                        ✕
                    </button>
                </div>
            </div>
        `);

        $('body').append($overlay);

        // Show with animation
        setTimeout(() => $overlay.addClass('show'), 50);

        // Close handlers
        $overlay.find('.simple-close-btn').on('click', closeSimpleFullscreen);
        $overlay.on('click', function(e) {
            if (e.target === this) {
                closeSimpleFullscreen();
            }
        });

        // ESC key to close
        $(document).on('keydown.fullscreen', function(e) {
            if (e.key === 'Escape') {
                closeSimpleFullscreen();
            }
        });
    }

    /**
     * Close simple fullscreen
     */
    function closeSimpleFullscreen() {
        const $overlay = $('.simple-fullscreen-overlay');
        $overlay.removeClass('show');
        $(document).off('keydown.fullscreen');
        setTimeout(() => $overlay.remove(), 300);
    }

    /**
     * Show elegant notification
     */
    function showElegantNotification(message, type = 'info') {
        const $notification = $(`
            <div class="elegant-notification ${type}">
                <div class="notification-content">
                    <span class="notification-message">${message}</span>
                    <button class="close-notification" aria-label="إغلاق">×</button>
                </div>
            </div>
        `);

        $('body').append($notification);

        setTimeout(() => $notification.addClass('show'), 100);

        // Auto hide after 3 seconds
        setTimeout(() => {
            $notification.removeClass('show');
            setTimeout(() => $notification.remove(), 300);
        }, 3000);

        // Manual close
        $notification.find('.close-notification').on('click', function() {
            $notification.removeClass('show');
            setTimeout(() => $notification.remove(), 300);
        });
    }

    // Initialize when document is ready
    $(document).ready(function() {
        initElegantAssetsInteractions();
    });

    // Make functions globally available
    window.ElegantAssetsInteractions = {
        init: initElegantAssetsInteractions,
        showNotification: showElegantNotification
    };

})(jQuery);
