/**
 * Single Auction Page - Assets Styles
 *
 * @package AuctionSystem
 * @version 1.0.0
 * @description أنماط بطاقات الأصول والصور والتفاصيل
 */

/* Assets Section */
.auction-assets {
    background: #fff;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.auction-assets h3 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

.assets-grid {
    display: grid;
    gap: 30px;
}

.no-assets {
    text-align: center;
    padding: 40px;
    color: #7f8c8d;
    font-style: italic;
}

/* Asset Card */
.asset-card {
    border: 1px solid #e1e8ed;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: #fff;
}

.asset-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.asset-header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #e1e8ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.asset-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.asset-type {
    background: #3498db;
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Enhanced Asset Gallery */
.asset-gallery {
    position: relative;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
}

.gallery-container {
    position: relative;
}

.main-image-container {
    position: relative;
    width: 100%;
    height: 350px;
    overflow: hidden;
    background: #f8f9fa;
}

.main-image-display {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
    cursor: zoom-in;
}

.main-image-display:hover {
    transform: scale(1.02);
}

.main-image-display:focus {
    outline: 3px solid #3498db;
    outline-offset: 2px;
}

/* Gallery Navigation */
.gallery-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 10;
    opacity: 0;
    visibility: hidden;
}

.main-image-container:hover .gallery-nav {
    opacity: 1;
    visibility: visible;
}

.gallery-prev {
    left: 15px;
}

.gallery-next {
    right: 15px;
}

.gallery-nav:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-50%) scale(1.1);
}

.gallery-nav:active {
    transform: translateY(-50%) scale(0.95);
}

.gallery-nav:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

/* Image Counter */
.image-counter {
    position: absolute;
    bottom: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    z-index: 10;
}

.image-counter .separator {
    margin: 0 4px;
    opacity: 0.7;
}

/* Enhanced Thumbnails */
.image-thumbnails {
    display: flex;
    gap: 12px;
    padding: 20px;
    background: #f8f9fa;
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: #bdc3c7 #ecf0f1;
}

.image-thumbnails::-webkit-scrollbar {
    height: 6px;
}

.image-thumbnails::-webkit-scrollbar-track {
    background: #ecf0f1;
    border-radius: 3px;
}

.image-thumbnails::-webkit-scrollbar-thumb {
    background: #bdc3c7;
    border-radius: 3px;
}

.image-thumbnails::-webkit-scrollbar-thumb:hover {
    background: #95a5a6;
}

.thumbnail-wrapper {
    flex-shrink: 0;
    width: 70px;
    height: 70px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.3s ease;
    position: relative;
}

.thumbnail-wrapper:hover {
    border-color: #3498db;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.thumbnail-wrapper.active {
    border-color: #2980b9;
    box-shadow: 0 4px 15px rgba(41, 128, 185, 0.4);
}

.thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.thumbnail-wrapper:hover .thumbnail-image {
    transform: scale(1.1);
}

/* Loading States */
.main-image-display[data-loading="true"] {
    opacity: 0.7;
    filter: blur(1px);
}

.thumbnail-wrapper[data-loading="true"] {
    opacity: 0.5;
}

/* Animation for image transitions */
@keyframes fadeInImage {
    from {
        opacity: 0;
        transform: scale(1.05);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.main-image-display.fade-in {
    animation: fadeInImage 0.4s ease-out;
}

/* Responsive Design for Enhanced Gallery */
@media (max-width: 768px) {
    .main-image-container {
        height: 280px;
    }

    .gallery-nav {
        width: 40px;
        height: 40px;
        opacity: 1;
        visibility: visible;
    }

    .gallery-prev {
        left: 10px;
    }

    .gallery-next {
        right: 10px;
    }

    .image-counter {
        bottom: 10px;
        right: 10px;
        padding: 6px 10px;
        font-size: 0.8rem;
    }

    .image-thumbnails {
        padding: 15px;
        gap: 8px;
    }

    .thumbnail-wrapper {
        width: 60px;
        height: 60px;
    }
}

@media (max-width: 480px) {
    .main-image-container {
        height: 250px;
    }

    .gallery-nav {
        width: 35px;
        height: 35px;
    }

    .gallery-nav svg {
        width: 18px;
        height: 18px;
    }

    .image-thumbnails {
        padding: 12px;
        gap: 6px;
    }

    .thumbnail-wrapper {
        width: 50px;
        height: 50px;
    }
}

/* Asset Details */
.asset-details {
    padding: 20px;
}

.details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-label {
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 500;
}

.detail-value {
    font-size: 1rem;
    color: #2c3e50;
    font-weight: 600;
}

.asset-description {
    margin: 15px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.asset-description p {
    margin: 0;
    color: #34495e;
    line-height: 1.6;
}

.asset-location {
    margin-top: 15px;
}

.location-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.location-link:hover {
    color: #2980b9;
}

/* Brochure Section */
.auction-brochure {
    background: #fff;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.auction-brochure h3 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

.brochure-card {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #bdc3c7;
    transition: all 0.3s ease;
}

.brochure-card:hover {
    border-color: #3498db;
    background: #ecf0f1;
}

.brochure-icon {
    color: #e74c3c;
    flex-shrink: 0;
}

.brochure-info h4 {
    font-size: 1.2rem;
    color: #2c3e50;
    margin-bottom: 8px;
}

.brochure-info p {
    color: #7f8c8d;
    margin-bottom: 15px;
    line-height: 1.5;
}

.download-brochure {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.download-brochure:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

/* Asset Images */
.asset-images {
    position: relative;
}

.image-gallery {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.gallery-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.gallery-item.active {
    opacity: 1;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-thumbnails {
    display: flex;
    gap: 10px;
    padding: 15px;
    background: #f8f9fa;
    overflow-x: auto;
}

.thumbnail {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.thumbnail.active {
    border-color: #3498db;
}

.thumbnail:hover {
    border-color: #2980b9;
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Asset Details */
.asset-details {
    padding: 25px;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item strong {
    color: #7f8c8d;
    font-size: 0.9rem;
    font-weight: 500;
}

.detail-item span {
    color: #2c3e50;
    font-weight: 600;
}

.asset-description {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.asset-description strong {
    color: #2c3e50;
    display: block;
    margin-bottom: 8px;
}

.asset-description p {
    margin: 0;
    color: #34495e;
    line-height: 1.6;
}

/* Price Information */
.price-info {
    display: flex;
    gap: 20px;
    margin: 20px 0;
    padding: 15px;
    background: linear-gradient(135deg, #e8f5e8, #f0f8ff);
    border-radius: 8px;
    border: 1px solid #27ae60;
}

.price-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.price-item strong {
    color: #27ae60;
    font-size: 0.9rem;
}

.price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
}

/* Asset Map */
.asset-map {
    margin-top: 20px;
}

.asset-map strong {
    display: block;
    margin-bottom: 10px;
    color: #2c3e50;
}

.map-container {
    border-radius: 8px;
    overflow: hidden;
    height: 200px;
    border: 1px solid #e1e8ed;
}

.map-container iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.map-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
    padding: 10px 15px;
    border: 1px solid #3498db;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.map-link:hover {
    background: #3498db;
    color: white;
}
