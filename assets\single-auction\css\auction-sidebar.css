/**
 * Single Auction Page - Sidebar Styles
 *
 * @package AuctionSystem
 * @version 1.0.0
 * @description أنماط الشريط الجانبي والبطاقات الجانبية
 */

/* Sidebar Cards */


.info-card h3 {
    font-size: 1.2rem;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

/* Company Information */
.company-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.detail-row {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-row strong {
    color: #7f8c8d;
    font-size: 0.9rem;
    font-weight: 500;
}

.detail-row span {
    color: #2c3e50;
    font-weight: 600;
}

.detail-row span a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.detail-row span a:hover {
    color: #2980b9;
}

/* Contact Card */
.contact-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.contact-card h3 {
    color: white;
    border-bottom-color: rgba(255, 255, 255, 0.3);
}

.contact-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.contact-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 12px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
}

.contact-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.phone-btn:hover {
    background: rgba(46, 204, 113, 0.2);
    border-color: #2ecc71;
}

.email-btn:hover {
    background: rgba(52, 152, 219, 0.2);
    border-color: #3498db;
}

.website-link {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
}

.website-link:hover {
    text-decoration: underline;
}

/* Brochure Card */
.brochure-content {
    text-align: center;
}

.brochure-icon {
    color: #e74c3c;
    margin-bottom: 15px;
}

.brochure-content p {
    color: #7f8c8d;
    margin-bottom: 20px;
    line-height: 1.6;
}

.btn-download {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-download:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

/* Auction Summary */
.summary-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f2f6;
}

.summary-row:last-child {
    border-bottom: none;
}

.summary-row strong {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.summary-row span {
    color: #2c3e50;
    font-weight: 600;
}

.countdown {
    color: #e74c3c !important;
    font-weight: 700 !important;
}
