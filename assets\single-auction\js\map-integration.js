/**
 * Location Preview and Map Integration for Auction Pages
 * Handles location display with Google Maps or OpenStreetMap
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeLocationPreview();
    initializeAuctionMap();
});

/**
 * تهيئة عرض موقع المزاد على الخريطة
 */
function initializeAuctionMap() {
    // التحقق من وجود الخريطة
    const mapElement = document.getElementById(auctionMapData?.map_id);
    if (!mapElement) return;
    
    // التحقق من وجود بيانات الخريطة
    if (!auctionMapData) return;
    
    // إذا كانت إحداثيات متوفرة، استخدم خريطة جوجل
    if (auctionMapData.latitude && auctionMapData.longitude) {
        initGoogleMap(mapElement, auctionMapData);
    } 
    // إذا كان العنوان فقط متوفر، استخدم صورة خريطة ثابتة
    else if (auctionMapData.address) {
        initStaticMap(mapElement, auctionMapData);
    }
}

/**
 * تهيئة خريطة جوجل التفاعلية
 */
function initGoogleMap(mapElement, mapData) {
    // إنشاء رابط خريطة جوجل للإحداثيات
    const googleMapsUrl = `https://www.google.com/maps/embed/v1/place?key=AIzaSyBtdO5k6CRntAypJhHJLLcT1IO0LTAzb60&q=${mapData.latitude},${mapData.longitude}&zoom=15&language=ar`;
    
    // إنشاء إطار لخريطة جوجل
    const iframe = document.createElement('iframe');
    iframe.src = googleMapsUrl;
    iframe.width = '100%';
    iframe.height = '100%';
    iframe.frameBorder = '0';
    iframe.style.border = '0';
    iframe.allowFullscreen = true;
    
    // إضافة الإطار للخريطة
    mapElement.innerHTML = '';
    mapElement.appendChild(iframe);
    
    // إزالة أي حالة تحميل
    const loadingElement = mapElement.parentElement.querySelector('.map-overlay');
    if (loadingElement) {
        loadingElement.remove();
    }
}

/**
 * استخدام صورة خريطة ثابتة باستخدام العنوان
 */
function initStaticMap(mapElement, mapData) {
    // إنشاء رابط لصورة خريطة ثابتة باستخدام العنوان
    const encodedAddress = encodeURIComponent(mapData.address);
    const staticMapUrl = `https://maps.googleapis.com/maps/api/staticmap?center=${encodedAddress}&zoom=15&size=600x300&maptype=roadmap&markers=color:red%7C${encodedAddress}&key=AIzaSyBtdO5k6CRntAypJhHJLLcT1IO0LTAzb60&language=ar`;
    
    // إنشاء عنصر الصورة
    const img = document.createElement('img');
    img.src = staticMapUrl;
    img.alt = 'خريطة موقع المزاد';
    img.style.width = '100%';
    img.style.height = '100%';
    img.style.objectFit = 'cover';
    
    // إضافة حدث النقر لفتح خريطة جوجل
    img.style.cursor = 'pointer';
    img.addEventListener('click', function() {
        window.open(`https://www.google.com/maps/search/?api=1&query=${encodedAddress}`, '_blank');
    });
    
    // إضافة الصورة للخريطة
    mapElement.innerHTML = '';
    mapElement.appendChild(img);
    
    // إزالة أي حالة تحميل
    const loadingElement = mapElement.parentElement.querySelector('.map-overlay');
    if (loadingElement) {
        loadingElement.remove();
    }
}

function initializeLocationPreview() {
    const mapWrappers = document.querySelectorAll('.map-wrapper');

    mapWrappers.forEach(wrapper => {
        const locationUrl = wrapper.dataset.locationUrl;
        if (locationUrl) {
            setupLocationInteraction(wrapper, locationUrl);
        }
    });
}

// Global functions for button actions
window.openDirections = function(locationUrl) {
    // Try to open directions in Google Maps
    let directionsUrl = locationUrl;

    // Convert to directions URL if it's a regular maps URL
    if (locationUrl.includes('google.com/maps') && !locationUrl.includes('dir/')) {
        // Extract coordinates or place info and create directions URL
        if (locationUrl.includes('@')) {
            const coords = locationUrl.match(/@(-?\d+\.\d+),(-?\d+\.\d+)/);
            if (coords) {
                directionsUrl = `https://www.google.com/maps/dir//${coords[1]},${coords[2]}`;
            }
        } else {
            // For place URLs, just add /dir/ before the place
            directionsUrl = locationUrl.replace('/place/', '/dir//');
        }
    }

    window.open(directionsUrl, '_blank');
};

window.shareLocation = function(locationUrl, title) {
    const shareText = `${title} - الموقع: ${locationUrl}`;

    // Try native sharing first (mobile devices)
    if (navigator.share) {
        navigator.share({
            title: title,
            text: 'موقع المزاد',
            url: locationUrl
        }).catch(err => {
            // Fallback to copy to clipboard
            copyLocationToClipboard(shareText);
        });
    } else {
        // Fallback to copy to clipboard
        copyLocationToClipboard(shareText);
    }
};

function copyLocationToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            showLocationMessage('تم نسخ الموقع إلى الحافظة!', 'success');
        }).catch(() => {
            fallbackCopyLocation(text);
        });
    } else {
        fallbackCopyLocation(text);
    }
}

function fallbackCopyLocation(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showLocationMessage('تم نسخ الموقع إلى الحافظة!', 'success');
    } catch (err) {
        showLocationMessage('فشل في نسخ الموقع', 'error');
    } finally {
        document.body.removeChild(textArea);
    }
}

function setupLocationInteraction(wrapper, locationUrl) {
    // Add click handler to the entire wrapper
    wrapper.addEventListener('click', function(e) {
        // Don't trigger if clicking on action buttons
        if (!e.target.closest('.map-action-btn')) {
            window.open(locationUrl, '_blank');
        }
    });

    // Add hover effect enhancement
    wrapper.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
    });

    wrapper.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
    });
}

function showLocationMessage(message, type = 'info') {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.location-message');
    existingMessages.forEach(msg => msg.remove());

    // Create message element
    const messageDiv = document.createElement('div');
    messageDiv.className = `location-message location-message-${type}`;
    messageDiv.textContent = message;

    // Style the message
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : '#3B82F6'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        font-size: 14px;
        font-weight: 500;
        max-width: 300px;
        animation: slideInRight 0.3s ease;
    `;

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;

    if (!document.querySelector('#location-message-styles')) {
        style.id = 'location-message-styles';
        document.head.appendChild(style);
    }

    // Add to page
    document.body.appendChild(messageDiv);

    // Auto remove after 3 seconds
    setTimeout(() => {
        messageDiv.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 300);
    }, 3000);
}

function setupMapInteraction(container, iframe) {
    // Add loading indicator
    addMapLoadingIndicator(container);

    // Handle iframe load events
    iframe.addEventListener('load', function() {
        removeMapLoadingIndicator(container);
        addMapLoadedClass(container);
    });

    // Handle iframe error
    iframe.addEventListener('error', function() {
        removeMapLoadingIndicator(container);
        showMapError(container);
    });

    // Add click-to-activate functionality for better UX
    setupClickToActivate(container, iframe);
}

function addMapLoadingIndicator(container) {
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'map-loading-indicator';
    loadingDiv.innerHTML = `
        <div class="loading-spinner">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
            </svg>
        </div>
        <p>جاري تحميل الخريطة...</p>
    `;

    container.appendChild(loadingDiv);
}

function removeMapLoadingIndicator(container) {
    const loadingIndicator = container.querySelector('.map-loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.remove();
    }
}

function addMapLoadedClass(container) {
    container.classList.add('map-loaded');

    // Add fade-in animation
    const iframe = container.querySelector('.google-map-embed');
    if (iframe) {
        iframe.style.opacity = '0';
        iframe.style.transition = 'opacity 0.3s ease';

        setTimeout(() => {
            iframe.style.opacity = '1';
        }, 100);
    }
}

function showMapError(container) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'map-error-indicator';
    errorDiv.innerHTML = `
        <div class="error-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="15" y1="9" x2="9" y2="15"></line>
                <line x1="9" y1="9" x2="15" y2="15"></line>
            </svg>
        </div>
        <p>فشل في تحميل الخريطة</p>
        <button class="retry-map-btn">إعادة المحاولة</button>
    `;

    container.appendChild(errorDiv);

    // Add retry functionality
    const retryBtn = errorDiv.querySelector('.retry-map-btn');
    retryBtn.addEventListener('click', function() {
        errorDiv.remove();
        const iframe = container.querySelector('.google-map-embed');
        if (iframe) {
            // Reload iframe
            const src = iframe.src;
            iframe.src = '';
            setTimeout(() => {
                iframe.src = src;
                addMapLoadingIndicator(container);
            }, 100);
        }
    });
}

function setupClickToActivate(container, iframe) {
    // Create overlay for click-to-activate
    const overlay = document.createElement('div');
    overlay.className = 'map-click-overlay';
    overlay.innerHTML = `
        <div class="click-to-activate">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
            </svg>
            <p>انقر لتفعيل الخريطة</p>
        </div>
    `;

    container.appendChild(overlay);

    // Remove overlay on click
    overlay.addEventListener('click', function() {
        overlay.remove();
        iframe.style.pointerEvents = 'auto';
    });

    // Initially disable iframe interaction
    iframe.style.pointerEvents = 'none';
}

// Add CSS for loading and error states
function addMapStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .map-loading-indicator {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.9);
            z-index: 10;
        }

        .loading-spinner {
            animation: mapLoadingSpin 2s linear infinite;
            margin-bottom: 1rem;
            color: var(--primary-color, #007cba);
        }

        @keyframes mapLoadingSpin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .map-error-indicator {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.95);
            z-index: 10;
            text-align: center;
            padding: 2rem;
        }

        .error-icon {
            color: #dc3545;
            margin-bottom: 1rem;
        }

        .retry-map-btn {
            background: var(--primary-color, #007cba);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 1rem;
            transition: background-color 0.2s ease;
        }

        .retry-map-btn:hover {
            background: var(--primary-dark, #005a87);
        }

        .map-click-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 5;
            cursor: pointer;
            transition: opacity 0.2s ease;
        }

        .map-click-overlay:hover {
            background: rgba(0, 0, 0, 0.4);
        }

        .click-to-activate {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            color: var(--text-primary, #333);
        }

        .click-to-activate svg {
            color: var(--primary-color, #007cba);
            margin-bottom: 0.5rem;
        }

        .map-loaded .google-map-embed {
            transition: opacity 0.3s ease;
        }
    `;

    document.head.appendChild(style);
}

// Initialize styles
addMapStyles();

// Handle window resize for responsive maps
window.addEventListener('resize', function() {
    const mapContainers = document.querySelectorAll('.map-wrapper');
    mapContainers.forEach(container => {
        // Trigger map resize if needed
        const iframe = container.querySelector('.google-map-embed');
        if (iframe && iframe.contentWindow) {
            try {
                // This might not work due to cross-origin restrictions, but it's worth trying
                iframe.contentWindow.postMessage('resize', '*');
            } catch (e) {
                // Silently fail - this is expected for cross-origin iframes
            }
        }
    });
});

// Export functions for external use
window.AuctionMapIntegration = {
    initialize: initializeMapIntegration,
    setupMap: setupMapInteraction
};
