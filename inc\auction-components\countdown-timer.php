<?php
/**
 * Countdown Timer Component
 *
 * @package AuctionSystem
 * @version 1.0.0
 * @description مكون عداد الوقت التنازلي للمزاد
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display auction countdown timer
 */
function auction_display_countdown_timer($auction_details, $auction_id) {
    ?>
    <!-- Enhanced Countdown Timer -->
    <?php
    // Check if date and time are available
    $iso_datetime = '';
    if (!empty($auction_details['auction_date']) && !empty($auction_details['auction_time'])) {
        $auction_datetime = $auction_details['auction_date'] . ' ' . $auction_details['auction_time'];
        $auction_timestamp = strtotime($auction_datetime);
        if ($auction_timestamp) {
            $iso_datetime = date('c', $auction_timestamp);
        }
    } elseif (!empty($auction_details['end_time_iso'])) {
        // Fallback: إذا كان هناك حقل end_time_iso جاهز
        $iso_datetime = $auction_details['end_time_iso'];
    } elseif (!empty($auction_details['end_time'])) {
        // Fallback: إذا كان هناك حقل end_time بصيغة timestamp أو ISO
        $iso_datetime = is_numeric($auction_details['end_time']) ? date('c', $auction_details['end_time']) : $auction_details['end_time'];
    }

    if ($iso_datetime) {
        // إذا كان التاريخ في المستقبل أو الحاضر
        if (strtotime($iso_datetime) > time()) {
            ?>
            <div class="auction-countdown"
                 id="auction-countdown-<?php echo $auction_id; ?>"
                 data-countdown-target="<?php echo esc_attr($iso_datetime); ?>"
                 data-auction-id="<?php echo $auction_id; ?>">
                <!-- Countdown will be populated by JavaScript -->
            </div>
        <?php } else { ?>
            <div class="auction-countdown">
                <div class="countdown-container">
                    <div class="countdown-expired">
                        <span class="expired-icon">⏰</span>
                        <span class="expired-text"><?php _e('انتهى المزاد', 'auction-system'); ?></span>
                    </div>
                </div>
            </div>
        <?php }
    } else { ?>
        <div class="auction-countdown">
            <div class="countdown-container">
                <div class="countdown-expired">
                    <span class="expired-icon">📅</span>
                    <span class="expired-text"><?php _e('موعد المزاد غير محدد', 'auction-system'); ?></span>
                </div>
            </div>
        </div>
    <?php } ?>
    <?php
}
