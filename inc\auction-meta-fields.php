<?php
/**
 * Auction Meta Fields Management
 * 
 * @package AuctionSystem
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Auction Meta Fields Class
 */
class AuctionMetaFields {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize
     */
    private function init() {
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_meta_fields'));
    }
    
    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        add_meta_box(
            'auction_details',
            __('تفاصيل المزاد', 'auction-system'),
            array($this, 'auction_details_callback'),
            'auction',
            'normal',
            'high'
        );
        
        add_meta_box(
            'company_details',
            __('معلومات الشركة', 'auction-system'),
            array($this, 'company_details_callback'),
            'auction',
            'normal',
            'high'
        );
        
        add_meta_box(
            'auction_brochure',
            __('البروشور', 'auction-system'),
            array($this, 'brochure_callback'),
            'auction',
            'side',
            'default'
        );
        
        add_meta_box(
            'auction_assets',
            __('الأصول', 'auction-system'),
            array($this, 'assets_callback'),
            'auction',
            'normal',
            'low'
        );
    }
    
    /**
     * Auction details meta box callback
     */
    public function auction_details_callback($post) {
        wp_nonce_field('auction_meta_nonce', 'auction_meta_nonce');
        
        $auction_date = get_post_meta($post->ID, '_auction_date', true);
        $auction_time = get_post_meta($post->ID, '_auction_time', true);
        $auction_city = get_post_meta($post->ID, '_auction_city', true);
        $auction_status = get_post_meta($post->ID, '_auction_status', true);
        
        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="auction_date"><?php _e('تاريخ المزاد', 'auction-system'); ?></label>
                </th>
                <td>
                    <input type="date" id="auction_date" name="auction_date" value="<?php echo esc_attr($auction_date); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="auction_time"><?php _e('وقت المزاد', 'auction-system'); ?></label>
                </th>
                <td>
                    <input type="time" id="auction_time" name="auction_time" value="<?php echo esc_attr($auction_time); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="auction_city"><?php _e('المدينة الرئيسية', 'auction-system'); ?></label>
                </th>
                <td>
                    <input type="text" id="auction_city" name="auction_city" value="<?php echo esc_attr($auction_city); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="auction_status"><?php _e('حالة المزاد', 'auction-system'); ?></label>
                </th>
                <td>
                    <select id="auction_status" name="auction_status" class="regular-text">
                        <option value="pending" <?php selected($auction_status, 'pending'); ?>><?php _e('في انتظار المراجعة', 'auction-system'); ?></option>
                        <option value="approved" <?php selected($auction_status, 'approved'); ?>><?php _e('معتمد', 'auction-system'); ?></option>
                        <option value="rejected" <?php selected($auction_status, 'rejected'); ?>><?php _e('مرفوض', 'auction-system'); ?></option>
                    </select>
                </td>
            </tr>
        </table>
        <?php
    }
    
    /**
     * Company details meta box callback
     */
    public function company_details_callback($post) {
        $company_name = get_post_meta($post->ID, '_company_name', true);
        $company_license = get_post_meta($post->ID, '_company_license', true);
        $company_contact = get_post_meta($post->ID, '_company_contact', true);
        $company_website = get_post_meta($post->ID, '_company_website', true);
        
        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="company_name"><?php _e('اسم الشركة', 'auction-system'); ?></label>
                </th>
                <td>
                    <input type="text" id="company_name" name="company_name" value="<?php echo esc_attr($company_name); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="company_license"><?php _e('رقم الترخيص', 'auction-system'); ?></label>
                </th>
                <td>
                    <input type="text" id="company_license" name="company_license" value="<?php echo esc_attr($company_license); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="company_contact"><?php _e('معلومات الاتصال', 'auction-system'); ?></label>
                </th>
                <td>
                    <textarea id="company_contact" name="company_contact" rows="3" class="large-text"><?php echo esc_textarea($company_contact); ?></textarea>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="company_website"><?php _e('الموقع الإلكتروني', 'auction-system'); ?></label>
                </th>
                <td>
                    <input type="url" id="company_website" name="company_website" value="<?php echo esc_attr($company_website); ?>" class="regular-text" />
                </td>
            </tr>
        </table>
        <?php
    }
    
    /**
     * Brochure meta box callback
     */
    public function brochure_callback($post) {
        $brochure_url = get_post_meta($post->ID, '_brochure_url', true);
        
        ?>
        <p>
            <label for="brochure_url"><?php _e('رابط البروشور', 'auction-system'); ?></label>
            <input type="url" id="brochure_url" name="brochure_url" value="<?php echo esc_attr($brochure_url); ?>" class="widefat" />
        </p>
        <p class="description">
            <?php _e('رفع ملف PDF يحتوي على تفاصيل المزاد والشروط والأحكام', 'auction-system'); ?>
        </p>
        <?php
    }
    
    /**
     * Assets meta box callback
     */
    public function assets_callback($post) {
        global $wpdb;
        
        $assets_table = $wpdb->prefix . 'auction_assets';
        $assets = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $assets_table WHERE auction_id = %d ORDER BY sort_order ASC",
            $post->ID
        ));
        
        ?>
        <div id="auction-assets-container">
            <p><?php _e('الأصول المرتبطة بهذا المزاد:', 'auction-system'); ?></p>
            
            <?php if ($assets): ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('العنوان', 'auction-system'); ?></th>
                            <th><?php _e('النوع', 'auction-system'); ?></th>
                            <th><?php _e('المساحة', 'auction-system'); ?></th>
                            <th><?php _e('الموقع', 'auction-system'); ?></th>
                            <th><?php _e('الإجراءات', 'auction-system'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($assets as $asset): ?>
                            <tr>
                                <td><?php echo esc_html($asset->title); ?></td>
                                <td><?php echo esc_html($this->get_asset_type_name($asset->asset_type_id)); ?></td>
                                <td><?php echo esc_html($asset->area); ?></td>
                                <td><?php echo esc_html($asset->city . ' - ' . $asset->district); ?></td>
                                <td>
                                    <a href="#" class="button button-small"><?php _e('تحرير', 'auction-system'); ?></a>
                                    <a href="#" class="button button-small button-link-delete"><?php _e('حذف', 'auction-system'); ?></a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p><?php _e('لا توجد أصول مرتبطة بهذا المزاد بعد.', 'auction-system'); ?></p>
            <?php endif; ?>
            
            <p>
                <a href="#" class="button button-primary"><?php _e('إضافة أصل جديد', 'auction-system'); ?></a>
            </p>
        </div>
        <?php
    }
    
    /**
     * Get asset type name by ID
     */
    private function get_asset_type_name($type_id) {
        global $wpdb;
        
        $types_table = $wpdb->prefix . 'auction_asset_types';
        $type_name = $wpdb->get_var($wpdb->prepare(
            "SELECT name FROM $types_table WHERE id = %d",
            $type_id
        ));
        
        return $type_name ? $type_name : __('غير محدد', 'auction-system');
    }
    
    /**
     * Save meta fields
     */
    public function save_meta_fields($post_id) {
        // Check if nonce is valid
        if (!isset($_POST['auction_meta_nonce']) || !wp_verify_nonce($_POST['auction_meta_nonce'], 'auction_meta_nonce')) {
            return;
        }
        
        // Check if user has permission to edit the post
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Check if not an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        // Check post type
        if (get_post_type($post_id) !== 'auction') {
            return;
        }
        
        // Save auction details
        $fields = array(
            'auction_date',
            'auction_time',
            'auction_city',
            'auction_status',
            'company_name',
            'company_license',
            'company_contact',
            'company_website',
            'brochure_url'
        );
        
        foreach ($fields as $field) {
            if (isset($_POST[$field])) {
                update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
            }
        }
    }
}
