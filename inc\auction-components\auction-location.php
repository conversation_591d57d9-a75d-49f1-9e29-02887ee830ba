<?php
/**
 * Auction Location Map Component
 * 
 * مكون عرض موقع المزاد على الخريطة
 * 
 * @package Auction System
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

if (!function_exists('auction_display_location_map')) {
    function auction_display_location_map($auction_details) {
        // تصحيح: طباعة بيانات المزاد لمعرفة الهيكل الصحيح
        echo '<!-- DEBUG: Auction Location Data -->';
        
        // التحقق من أسماء المفاتيح المتاحة في بيانات المزاد
        $available_keys = array_keys($auction_details);
        echo '<!-- Available keys: ' . implode(', ', $available_keys) . ' -->';
        
        // التحقق من وجود إحداثيات - نحاول البحث عن مفاتيح محتملة
        $lat_keys = ['latitude', 'lat', 'auction_latitude', 'geo_lat'];
        $lng_keys = ['longitude', 'lng', 'long', 'auction_longitude', 'geo_lng'];
        
        $latitude = '';
        $longitude = '';
        
        // البحث عن مفتاح الإحداثيات
        foreach ($lat_keys as $key) {
            if (isset($auction_details[$key]) && !empty($auction_details[$key])) {
                $latitude = $auction_details[$key];
                echo '<!-- Found latitude in key: ' . $key . ' with value: ' . $latitude . ' -->';
                break;
            }
        }
        
        foreach ($lng_keys as $key) {
            if (isset($auction_details[$key]) && !empty($auction_details[$key])) {
                $longitude = $auction_details[$key];
                echo '<!-- Found longitude in key: ' . $key . ' with value: ' . $longitude . ' -->';
                break;
            }
        }
        
        // التحقق من وجود عنوان - نحاول البحث عن مفاتيح محتملة
        $address_keys = ['address', 'auction_address', 'location', 'auction_location'];
        $city_keys = ['city', 'auction_city', 'town', 'region'];
        
        $address = '';
        $city = '';
        
        // البحث عن مفتاح العنوان
        foreach ($address_keys as $key) {
            if (isset($auction_details[$key]) && !empty($auction_details[$key])) {
                $address = $auction_details[$key];
                echo '<!-- Found address in key: ' . $key . ' with value: ' . $address . ' -->';
                break;
            }
        }
        
        foreach ($city_keys as $key) {
            if (isset($auction_details[$key]) && !empty($auction_details[$key])) {
                $city = $auction_details[$key];
                echo '<!-- Found city in key: ' . $key . ' with value: ' . $city . ' -->';
                break;
            }
        }
        
        $full_address = trim($address . ' ' . $city);
        echo '<!-- Full address: ' . $full_address . ' -->';
        
        // إذا لم تتوفر الإحداثيات أو العنوان، أظهر رسالة تصحيح وخروج
        if ((empty($latitude) || empty($longitude)) && empty($full_address)) {
            echo '<!-- DEBUG: No location data found. Cannot display map. -->';
            return;
        }
        
        // تعيين معرف فريد للخريطة
        $map_id = 'auction-map-' . uniqid();
        echo '<!-- Map ID: ' . $map_id . ' -->';
        
        // أنماط CSS للخريطة
        // تأكد من تحميل CSS مباشرة في الصفحة
        echo '<style>
            .auction-location-section {
                margin: 25px 0;
                border-radius: 8px;
                background-color: #fff;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                overflow: hidden;
            }
            .auction-location-section .section-header {
                padding: 15px 20px;
                border-bottom: 1px solid #eee;
                background-color: #f8f9fa;
            }
            .auction-location-section .section-header h3 {
                margin: 0;
                font-size: 18px;
                color: #333;
                display: flex;
                align-items: center;
            }
            .auction-location-section .section-header h3 i {
                margin-left: 10px;
                color: #e74c3c;
                font-size: 20px;
            }
            .auction-location-section .section-content {
                padding: 20px;
            }
            .location-address {
                margin-bottom: 15px;
                padding: 10px 15px;
                background-color: #f8f9fa;
                border-radius: 5px;
            }
            .location-address p {
                margin: 0;
                font-size: 15px;
                color: #555;
                display: flex;
                align-items: center;
            }
            .location-address p i {
                margin-left: 10px;
                color: #3498db;
            }
            .auction-map-container {
                position: relative;
                width: 100%;
                height: 300px;
                border-radius: 8px;
                overflow: hidden;
                margin-bottom: 15px;
                border: 1px solid #ddd;
            }
            .auction-map {
                width: 100%;
                height: 100%;
            }
            .map-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(255, 255, 255, 0.7);
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .map-loading {
                background-color: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                display: flex;
                align-items: center;
            }
            .map-actions {
                display: flex;
                justify-content: center;
            }
            .btn-directions {
                background-color: #3498db;
                color: white;
                padding: 8px 20px;
                border-radius: 5px;
                text-decoration: none;
                font-weight: bold;
                display: inline-flex;
                align-items: center;
            }
            .btn-directions i {
                margin-left: 8px;
            }
            .btn-directions:hover {
                background-color: #2980b9;
                color: white;
            }
        </style>';
        
        // بيانات الخريطة - سنستخدم خريطة صورة ثابتة لتجنب المشاكل مع تحميل الجافاسكريبت
        $map_data = array(
            'map_id' => $map_id,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'address' => $full_address,
            'title' => isset($auction_details['title']) ? $auction_details['title'] : '',
        );
        
        // سنستخدم طريقة بسيطة للخريطة بدلاً من الاعتماد على ملف جافاسكريبت منفصل
        echo '<!-- Map data: ' . json_encode($map_data) . ' -->';
        
        ?>
        <div class="auction-location-section">
            <div class="section-header">
                <h3><i class="fas fa-map-marker-alt"></i> <?php _e('موقع المزاد', 'auction-system'); ?></h3>
            </div>
            <div class="section-content">
                <?php if (!empty($full_address)): ?>
                <div class="location-address">
                    <p><i class="fas fa-location-arrow"></i> <?php echo esc_html($full_address); ?></p>
                </div>
                <?php endif; ?>
                
                <div class="auction-map-container">
                    <?php 
                    // عرض الخريطة باستخدام النهج المناسب بناءً على البيانات المتوفرة
                    if (!empty($latitude) && !empty($longitude)) {
                        // استخدام iframe لخريطة جوجل مع الإحداثيات
                        echo '<iframe 
                            width="100%" 
                            height="100%" 
                            frameborder="0" 
                            style="border:0" 
                            src="https://www.google.com/maps/embed/v1/place?key=AIzaSyBtdO5k6CRntAypJhHJLLcT1IO0LTAzb60&q=' . esc_attr($latitude) . ',' . esc_attr($longitude) . '&zoom=15&language=ar"
                            allowfullscreen>
                        </iframe>';
                    } 
                    elseif (!empty($full_address)) {
                        // استخدام صورة خريطة ثابتة مع العنوان
                        $encoded_address = urlencode($full_address);
                        $map_img_url = "https://maps.googleapis.com/maps/api/staticmap?center={$encoded_address}&zoom=15&size=600x300&maptype=roadmap&markers=color:red%7C{$encoded_address}&key=AIzaSyBtdO5k6CRntAypJhHJLLcT1IO0LTAzb60&language=ar";
                        echo '<a href="https://www.google.com/maps/search/?api=1&query=' . $encoded_address . '" target="_blank">';
                        echo '<img src="' . esc_url($map_img_url) . '" alt="خريطة موقع المزاد" style="width:100%; height:100%; object-fit:cover;">';
                        echo '</a>';
                    }
                    else {
                        // إذا لم يكن هناك بيانات كافية، عرض رسالة
                        echo '<div class="map-overlay" style="position:static; background-color:#f8f9fa; height:100%; text-align:center;">';
                        echo '<div style="padding:20px;">';
                        echo '<i class="fas fa-map-marked-alt" style="font-size:48px; color:#ddd; margin-bottom:15px; display:block;"></i>';
                        echo '<p>' . __('لم يتم تحديد موقع لهذا المزاد بعد', 'auction-system') . '</p>';
                        echo '</div>';
                        echo '</div>';
                    }
                    ?>
                </div>
                
                <?php if (!empty($latitude) && !empty($longitude)): ?>
                <div class="map-actions">
                    <a href="https://www.google.com/maps/dir/?api=1&destination=<?php echo esc_attr($latitude); ?>,<?php echo esc_attr($longitude); ?>" class="btn btn-directions" target="_blank">
                        <i class="fas fa-directions"></i> <?php _e('الاتجاهات للموقع', 'auction-system'); ?>
                    </a>
                </div>
                <?php elseif (!empty($full_address)): ?>
                <div class="map-actions">
                    <a href="https://www.google.com/maps/search/?api=1&query=<?php echo urlencode($full_address); ?>" class="btn btn-directions" target="_blank">
                        <i class="fas fa-directions"></i> <?php _e('الاتجاهات للموقع', 'auction-system'); ?>
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }
}
?>
