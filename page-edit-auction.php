<?php
/**
 * Template Name: Edit Auction
 *
 * @package AuctionSystem
 * @version 1.0.0
 * @description صفحة تعديل المزاد - نفس تصميم الإنشاء لكن للتعديل
 */

// Security check
if (!defined('ABSPATH')) {
    exit;
}

// Check if user can edit auctions
if (!current_user_can('edit_posts')) {
    wp_die('غير مسموح لك بتعديل المزادات');
}

// Get auction ID
$auction_id = isset($_GET['auction_id']) ? intval($_GET['auction_id']) : 0;

if (!$auction_id) {
    wp_die('معرف المزاد غير صحيح');
}

// Get auction data
$edit_handler = AuctionEditHandler::get_instance();
$auction_data = $edit_handler->get_auction_data($auction_id);

if (!$auction_data) {
    wp_die('المزاد غير موجود');
}

// Get asset types
$asset_types = $edit_handler->get_asset_types();

// Include required files
require_once get_stylesheet_directory() . '/inc/auction-frontend.php';

// Initialize frontend handler
$auction_frontend = new AuctionFrontend();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $result = $auction_frontend->handle_form_submission();

    if ($result['success']) {
        // Show success message
        get_header();

        // Enqueue required scripts and styles
        wp_enqueue_script('auction-create-form', get_stylesheet_directory_uri() . '/assets/auction-script.js', array('jquery'), AUCTION_VERSION, true);
        wp_enqueue_style('auction-create-form', get_stylesheet_directory_uri() . '/assets/auction-style.css', array(), AUCTION_VERSION);

        $form_renderer = AuctionFormRenderer::get_instance();
        $form_renderer->render_success('edit');

        get_footer();
        return;
    } else {
        // Show error message
        $error_message = $result['message'];
    }
}

get_header();

// Enqueue required scripts and styles
wp_enqueue_script('auction-create-form', get_stylesheet_directory_uri() . '/assets/auction-script.js', array('jquery'), AUCTION_VERSION, true);
wp_enqueue_style('auction-create-form', get_stylesheet_directory_uri() . '/assets/auction-style.css', array(), AUCTION_VERSION);
?>

<?php
// Use the unified form renderer
$form_renderer = AuctionFormRenderer::get_instance();
$form_renderer->render_form('edit', $auction_data, isset($error_message) ? $error_message : null);
?>

<?php get_footer(); ?>
