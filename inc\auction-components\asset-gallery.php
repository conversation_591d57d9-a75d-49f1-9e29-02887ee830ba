<?php
/**
 * Asset Gallery Component
 *
 * @package AuctionSystem
 * @version 1.0.0
 * @description مكون معرض الصور للأصول
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display horizontal asset gallery with auto slideshow
 */
function auction_display_horizontal_gallery($asset) {
    if (!empty($asset['images'])): ?>
        <div class="horizontal-asset-gallery" data-asset-id="<?php echo $asset['id']; ?>">
            <div class="auto-slideshow-container">
                <?php foreach ($asset['images'] as $img_index => $image): ?>
                    <img src="<?php echo esc_url($image); ?>"
                         alt="<?php echo esc_attr($asset['title']); ?> - <?php echo $img_index + 1; ?>"
                         class="slideshow-image <?php echo $img_index === 0 ? 'active' : ''; ?>"
                         data-image-index="<?php echo $img_index; ?>">
                <?php endforeach; ?>

                <!-- Image Counter -->
                <div class="image-overlay-info">
                    <span class="current-image">1</span> / <span class="total-images"><?php echo count($asset['images']); ?></span>
                </div>

                <!-- Simple Fullscreen Button -->
                <button class="simple-fullscreen-btn" title="عرض بالحجم الكامل">
                    ⛶
                </button>



                <?php if (count($asset['images']) > 1): ?>
                    <!-- Slideshow Indicators -->
                    <div class="slideshow-indicators">
                        <?php foreach ($asset['images'] as $img_index => $image): ?>
                            <div class="slideshow-dot <?php echo $img_index === 0 ? 'active' : ''; ?>"
                                 data-image-index="<?php echo $img_index; ?>"></div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif;
}

/**
 * Display elegant asset gallery with enhanced design (Legacy)
 */
function auction_display_asset_gallery($asset) {
    if (!empty($asset['images'])): ?>
        <div class="elegant-asset-gallery" data-asset-id="<?php echo $asset['id']; ?>">
            <div class="elegant-gallery-main">
                <!-- Main Image Display -->
                <div class="elegant-main-image-wrapper">
                    <div class="elegant-image-container">
                        <img src="<?php echo esc_url($asset['images'][0]); ?>"
                             alt="<?php echo esc_attr($asset['title']); ?>"
                             class="elegant-main-image"
                             data-image-index="0">


                    </div>

                    <?php if (count($asset['images']) > 1): ?>
                        <!-- Navigation Controls -->
                        <div class="elegant-gallery-controls">
                            <button class="elegant-nav-btn prev-btn" aria-label="<?php _e('الصورة السابقة', 'auction-system'); ?>">
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="15,18 9,12 15,6"></polyline>
                                </svg>
                            </button>

                            <div class="elegant-image-indicator">
                                <span class="elegant-current-num">1</span>
                                <span class="elegant-divider">من</span>
                                <span class="elegant-total-num"><?php echo count($asset['images']); ?></span>
                            </div>

                            <button class="elegant-nav-btn next-btn" aria-label="<?php _e('الصورة التالية', 'auction-system'); ?>">
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="9,18 15,12 9,6"></polyline>
                                </svg>
                            </button>
                        </div>
                    <?php endif; ?>
                </div>

                <?php if (count($asset['images']) > 1): ?>
                    <!-- Thumbnail Strip -->
                    <div class="elegant-thumbnail-strip">
                        <div class="elegant-thumbnails-container">
                            <?php foreach ($asset['images'] as $img_index => $image): ?>
                                <div class="elegant-thumb-item <?php echo $img_index === 0 ? 'active' : ''; ?>"
                                     data-image-index="<?php echo $img_index; ?>">
                                    <img src="<?php echo esc_url($image); ?>"
                                         alt="<?php echo esc_attr($asset['title']); ?> - <?php echo ($img_index + 1); ?>"
                                         class="elegant-thumb-image"
                                         loading="lazy">
                                    <div class="elegant-thumb-overlay"></div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Preload hidden images -->
            <div class="preload-images" style="display: none;">
                <?php foreach ($asset['images'] as $img_index => $image): ?>
                    <?php if ($img_index > 0): ?>
                        <img src="<?php echo esc_url($image); ?>" alt="" loading="lazy">
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif;
}

/**
 * Display elegant asset details section
 */
function auction_display_asset_details($asset) {
    ?>
    <div class="elegant-asset-details">
        <!-- Asset Info Cards -->
        <div class="elegant-info-cards-grid">
            <!-- Area Card -->
            <div class="elegant-info-card area-card">
                <div class="elegant-card-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <path d="M9 9h6v6H9z"></path>
                    </svg>
                </div>
                <div class="elegant-card-content">
                    <span class="elegant-card-label"><?php _e('المساحة', 'auction-system'); ?></span>
                    <span class="elegant-card-value"><?php echo esc_html($asset['area']); ?> <small><?php _e('م²', 'auction-system'); ?></small></span>
                </div>
            </div>

            <!-- Deed Number Card -->
            <?php if (!empty($asset['deed_number']) && trim($asset['deed_number']) !== ''): ?>
            <div class="elegant-info-card deed-card">
                <div class="elegant-card-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14,2 14,8 20,8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10,9 9,9 8,9"></polyline>
                    </svg>
                </div>
                <div class="elegant-card-content">
                    <span class="elegant-card-label"><?php _e('رقم الصك7777', 'auction-system'); ?></span>
                    <span class="elegant-card-value"><?php echo esc_html($asset['deed_number']); ?></span>
                </div>
            </div>
            <?php endif; ?>

            <!-- Location Card -->
            <div class="elegant-info-card location-card">
                <div class="elegant-card-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                        <circle cx="12" cy="10" r="3"></circle>
                    </svg>
                </div>
                <div class="elegant-card-content">
                    <span class="elegant-card-label"><?php _e('الموقع', 'auction-system'); ?></span>
                    <span class="elegant-card-value"><?php echo esc_html($asset['city']); ?><?php echo !empty($asset['district']) ? ' - ' . esc_html($asset['district']) : ''; ?></span>
                </div>
            </div>
        </div>

        <?php if (!empty($asset['description'])): ?>
            <!-- Collapsible Description Section -->
            <div class="elegant-description-section">
                <button class="elegant-description-toggle" type="button" aria-expanded="false">
                    <svg class="description-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14,2 14,8 20,8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10,9 9,9 8,9"></polyline>
                    </svg>
                    <span class="description-title"><?php _e('وصف العقار', 'auction-system'); ?></span>
                    <svg class="toggle-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="6,9 12,15 18,9"></polyline>
                    </svg>
                </button>
                <div class="elegant-description-content" style="display: none;">
                    <p><?php echo wp_kses_post(nl2br($asset['description'])); ?></p>
                </div>
            </div>
        <?php endif; ?>

        <!-- Action Buttons -->
        <div class="elegant-asset-actions">
            <?php if (!empty($asset['location_url'])): ?>
                <a href="<?php echo esc_url($asset['location_url']); ?>"
                   target="_blank"
                   rel="noopener noreferrer"
                   class="elegant-action-btn elegant-map-btn"
                   title="<?php _e('عرض على الخريطة', 'auction-system'); ?>"
                   aria-label="<?php _e('عرض على الخريطة', 'auction-system'); ?>">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                        <circle cx="12" cy="10" r="3"></circle>
                    </svg>
                </a>
            <?php endif; ?>

            <button class="elegant-action-btn elegant-share-btn" data-asset-title="<?php echo esc_attr($asset['title']); ?>">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path>
                    <polyline points="16,6 12,2 8,6"></polyline>
                    <line x1="12" y1="2" x2="12" y2="15"></line>
                </svg>
                <?php _e('مشاركة العقار', 'auction-system'); ?>
            </button>
        </div>
    </div>

    <?php
    // Enqueue elegant assets CSS and JS
    wp_enqueue_style(
        'elegant-assets-css',
        get_stylesheet_directory_uri() . '/assets/single-auction/css/elegant-assets.css',
        array(),
        AUCTION_VERSION
    );

    wp_enqueue_style(
        'elegant-gallery-css',
        get_stylesheet_directory_uri() . '/assets/single-auction/css/elegant-gallery.css',
        array('elegant-assets-css'),
        AUCTION_VERSION
    );

    wp_enqueue_style(
        'elegant-asset-details-css',
        get_stylesheet_directory_uri() . '/assets/single-auction/css/elegant-asset-details.css',
        array('elegant-assets-css'),
        AUCTION_VERSION
    );

    wp_enqueue_script(
        'elegant-assets-interactions-js',
        get_stylesheet_directory_uri() . '/assets/single-auction/js/elegant-assets-interactions.js',
        array('jquery'),
        AUCTION_VERSION,
        true
    );
    ?>
    <?php
}
