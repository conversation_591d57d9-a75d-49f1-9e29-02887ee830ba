/**
 * Auction Map Styles
 */

.auction-location-section {
    margin: 25px 0;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.auction-location-section .section-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
}

.auction-location-section .section-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
    display: flex;
    align-items: center;
}

.auction-location-section .section-header h3 i {
    margin-left: 10px;
    color: #e74c3c;
    font-size: 20px;
}

.auction-location-section .section-content {
    padding: 20px;
}

.location-address {
    margin-bottom: 15px;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.location-address p {
    margin: 0;
    font-size: 15px;
    color: #555;
    display: flex;
    align-items: center;
}

.location-address p i {
    margin-left: 10px;
    color: #3498db;
}

.auction-map-container {
    position: relative;
    width: 100%;
    height: 300px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 15px;
    border: 1px solid #ddd;
}

.auction-map {
    width: 100%;
    height: 100%;
}

.map-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
}

.map-loading {
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    display: flex;
    align-items: center;
}

.map-loading i {
    margin-left: 10px;
}

.map-actions {
    display: flex;
    justify-content: center;
}

.btn-directions {
    background-color: #3498db;
    color: white;
    padding: 8px 20px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    transition: background-color 0.3s;
}

.btn-directions i {
    margin-left: 8px;
}

.btn-directions:hover {
    background-color: #2980b9;
    color: white;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .auction-map-container {
        height: 250px;
    }
}

@media (max-width: 480px) {
    .auction-map-container {
        height: 200px;
    }
}
