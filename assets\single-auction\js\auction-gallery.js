/**
 * Single Auction Page - Image Gallery Management
 *
 * @package AuctionSystem
 * @version 1.0.0
 * @description إدارة معارض الصور والتنقل بينها
 */

(function($) {
    'use strict';

    /**
     * Initialize image galleries for assets
     */
    function initializeImageGalleries() {
        $('.asset-card').each(function() {
            const $assetCard = $(this);
            const $thumbnails = $assetCard.find('.thumbnail');
            const $galleryItems = $assetCard.find('.gallery-item');

            // Bind thumbnail click events
            $thumbnails.on('click', function() {
                const index = $(this).data('index');

                // Update active thumbnail
                $thumbnails.removeClass('active');
                $(this).addClass('active');

                // Update active gallery item
                $galleryItems.removeClass('active');
                $galleryItems.eq(index).addClass('active');
            });

            // Add keyboard navigation
            $assetCard.on('keydown', function(e) {
                if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                    e.preventDefault();
                    const $activeThumbnail = $thumbnails.filter('.active');
                    const currentIndex = $activeThumbnail.data('index');
                    let newIndex;

                    if (e.key === 'ArrowLeft') {
                        newIndex = currentIndex > 0 ? currentIndex - 1 : $thumbnails.length - 1;
                    } else {
                        newIndex = currentIndex < $thumbnails.length - 1 ? currentIndex + 1 : 0;
                    }

                    $thumbnails.eq(newIndex).trigger('click');
                }
            });

            // Add swipe support for mobile
            let startX = 0;
            let endX = 0;

            $assetCard.find('.image-gallery').on('touchstart', function(e) {
                startX = e.originalEvent.touches[0].clientX;
            });

            $assetCard.find('.image-gallery').on('touchend', function(e) {
                endX = e.originalEvent.changedTouches[0].clientX;
                handleSwipe();
            });

            function handleSwipe() {
                const swipeThreshold = 50;
                const diff = startX - endX;

                if (Math.abs(diff) > swipeThreshold) {
                    const $activeThumbnail = $thumbnails.filter('.active');
                    const currentIndex = $activeThumbnail.data('index');
                    let newIndex;

                    if (diff > 0) {
                        // Swipe left - next image
                        newIndex = currentIndex < $thumbnails.length - 1 ? currentIndex + 1 : 0;
                    } else {
                        // Swipe right - previous image
                        newIndex = currentIndex > 0 ? currentIndex - 1 : $thumbnails.length - 1;
                    }

                    $thumbnails.eq(newIndex).trigger('click');
                }
            }
        });
    }

    /**
     * Show image in modal
     */
    function showImageModal(src, alt) {
        const modal = $(`
            <div class="image-modal" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                cursor: pointer;
            ">
                <div style="
                    max-width: 90%;
                    max-height: 90%;
                    position: relative;
                ">
                    <img src="${src}" alt="${alt}" style="
                        max-width: 100%;
                        max-height: 100%;
                        object-fit: contain;
                    ">
                    <button style="
                        position: absolute;
                        top: -40px;
                        right: 0;
                        background: white;
                        border: none;
                        border-radius: 50%;
                        width: 30px;
                        height: 30px;
                        cursor: pointer;
                        font-size: 18px;
                        line-height: 1;
                    ">&times;</button>
                </div>
            </div>
        `);

        $('body').append(modal);

        modal.on('click', function(e) {
            if (e.target === this || $(e.target).is('button')) {
                modal.remove();
            }
        });

        // Close on escape key
        $(document).on('keydown.imageModal', function(e) {
            if (e.key === 'Escape') {
                modal.remove();
                $(document).off('keydown.imageModal');
            }
        });
    }

    // Make functions globally available
    window.AuctionGallery = {
        initializeImageGalleries: initializeImageGalleries,
        showImageModal: showImageModal
    };



})(jQuery);
