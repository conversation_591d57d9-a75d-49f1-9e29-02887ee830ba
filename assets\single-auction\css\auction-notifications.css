/**
 * Auction Notifications and Interactive Elements
 * 
 * @package AuctionSystem
 * @version 2.0.0
 * @description أنماط الإشعارات والعناصر التفاعلية
 */

/* ========================================
   NOTIFICATION SYSTEM
   ======================================== */
.auction-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--background);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-xl);
    z-index: 9999;
    min-width: 300px;
    max-width: 400px;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-md);
}

.auction-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.auction-notification.success {
    border-left: 4px solid var(--success);
    background: rgba(5, 150, 105, 0.05);
}

.auction-notification.error {
    border-left: 4px solid var(--danger);
    background: rgba(220, 38, 38, 0.05);
}

.auction-notification.info {
    border-left: 4px solid var(--primary-red);
    background: rgba(220, 38, 38, 0.05);
}

.close-notification {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.close-notification:hover {
    background: var(--background-gray);
    color: var(--text-primary);
}

/* ========================================
   SCROLL PROGRESS BAR
   ======================================== */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-red), #f59e0b);
    z-index: 9998;
    transition: width 0.1s ease;
}

/* ========================================
   LOADING STATES
   ======================================== */
.asset-image[data-loading="true"] {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    min-height: 200px;
    border-radius: var(--radius-md);
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--radius-md);
}

.skeleton-text {
    height: 1rem;
    margin-bottom: var(--spacing-sm);
}

.skeleton-text.large {
    height: 1.5rem;
}

.skeleton-text.small {
    height: 0.75rem;
    width: 60%;
}

/* ========================================
   ENHANCED ANIMATIONS
   ======================================== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

/* ========================================
   INTERACTIVE STATES
   ======================================== */
.interactive-element {
    transition: all var(--transition-normal);
    cursor: pointer;
}

.interactive-element:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.interactive-element:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

.interactive-element:focus {
    outline: 2px solid var(--primary-red);
    outline-offset: 2px;
}

/* ========================================
   TOOLTIP SYSTEM
   ======================================== */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-dark);
    color: var(--background);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    z-index: 1000;
    margin-bottom: var(--spacing-sm);
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--primary-dark);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    z-index: 1000;
}

.tooltip:hover::before,
.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* ========================================
   ENHANCED BUTTONS
   ======================================== */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-enhanced:active {
    transform: translateY(0);
}

/* ========================================
   MOBILE OPTIMIZATIONS
   ======================================== */
@media (max-width: 768px) {
    .auction-notification {
        right: 10px;
        left: 10px;
        min-width: auto;
        max-width: none;
        transform: translateY(-100%);
    }
    
    .auction-notification.show {
        transform: translateY(0);
    }
    
    .scroll-progress {
        height: 2px;
    }
    
    .tooltip::before {
        font-size: 0.75rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}

/* ========================================
   ACCESSIBILITY IMPROVEMENTS
   ======================================== */
@media (prefers-reduced-motion: reduce) {
    .auction-notification,
    .interactive-element,
    .btn-enhanced,
    .tooltip::before,
    .tooltip::after {
        transition: none !important;
        animation: none !important;
    }
    
    .asset-image[data-loading="true"],
    .loading-skeleton {
        animation: none !important;
        background: #f0f0f0 !important;
    }
}

/* Focus indicators for keyboard navigation */
.action-btn:focus-visible,
.contact-btn:focus-visible,
.load-more-button:focus-visible,
.location-link:focus-visible,
.download-brochure:focus-visible {
    outline: 2px solid var(--primary-red);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.1);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .auction-notification {
        border: 2px solid;
    }
    
    .scroll-progress {
        background: #000000;
    }
    
    .loading-skeleton,
    .asset-image[data-loading="true"] {
        background: repeating-linear-gradient(
            90deg,
            #000000 0px,
            #000000 10px,
            #ffffff 10px,
            #ffffff 20px
        ) !important;
    }
}
