<?php
/**
 * Auction Assets Component
 *
 * @package AuctionSystem
 * @version 2.0.0
 * @description مكون الأصول المعروضة في المزاد مع دعم التحميل التدريجي
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Include required component files
require_once get_stylesheet_directory() . '/inc/auction-components/asset-gallery.php';
require_once get_stylesheet_directory() . '/templates/asset-card-template.php';

// Include AuctionSettings class if not exists
if (!class_exists('AuctionSettings')) {
    require_once get_stylesheet_directory() . '/inc/auction-settings.php';
}

// Get AuctionSettings instance
$auction_settings = AuctionSettings::get_instance();

/**
 * Get assets for an auction with pagination
 */
function get_auction_assets_paginated($auction_id, $page = 1, $per_page = null) {
    global $auction_settings;
    
    // Use default from auction settings if not provided
    if ($per_page === null) {
        $settings = $auction_settings->get_all_settings();
        $per_page = isset($settings['lazy_load_per_page']) ? (int)$settings['lazy_load_per_page'] : 10;
    }
    global $wpdb;
    
    $offset = ($page - 1) * $per_page;
    $assets_table = $wpdb->prefix . 'auction_assets';
    
    // Get paginated assets
    $assets = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT * FROM {$assets_table} 
             WHERE auction_id = %d 
             ORDER BY sort_order ASC 
             LIMIT %d OFFSET %d",
            $auction_id,
            $per_page,
            $offset
        ),
        ARRAY_A
    );
    
    // Get total count for pagination
    $total_items = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT COUNT(*) FROM {$assets_table} WHERE auction_id = %d",
            $auction_id
        )
    );
    
    // Process assets data
    $processed_assets = array();
    foreach ($assets as $asset) {
        // Decode images JSON
        $asset['images'] = !empty($asset['images']) ? json_decode($asset['images'], true) : array();
        $processed_assets[] = $asset;
    }
    
    return array(
        'items' => $processed_assets,
        'total' => (int)$total_items,
        'pages' => ceil($total_items / $per_page),
        'current_page' => $page
    );
}

/**
 * Display elegant auction assets section with lazy loading
 */
function auction_display_assets($auction_details) {
    global $auction_settings, $wpdb;
    
    $auction_id = $auction_details['id'];
    
    // Get settings from AuctionSettings
    $settings = $auction_settings->get_all_settings();
    $lazy_load = isset($settings['lazy_load_enabled']) ? (bool)$settings['lazy_load_enabled'] : true;
    $items_per_page = isset($settings['lazy_load_per_page']) ? (int)$settings['lazy_load_per_page'] : 10;
    $auto_load = isset($settings['lazy_load_auto_load']) ? (bool)$settings['lazy_load_auto_load'] : true;
    $animation = isset($settings['lazy_load_animation']) ? $settings['lazy_load_animation'] : 'fade';
    
    // Get initial assets
    $assets_data = get_auction_assets_paginated($auction_id, 1, $items_per_page);
    
    $scroll_distance = isset($settings['lazy_load_scroll_distance']) ? (int)$settings['lazy_load_scroll_distance'] : 300;
    
    // Ensure no other queries interfere with our pagination
    wp_reset_postdata();
    wp_reset_query();
    
    // Localize script with settings
    wp_localize_script('auction-main', 'auctionData', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('load_more_assets'),
        'auction_id' => $auction_id,
        'current_page' => 1,
        'max_pages' => $assets_data['pages'],
        'lazy_settings' => array(
            'lazy_load_enabled' => $lazy_load,
            'lazy_load_auto_load' => $auto_load,
            'lazy_load_items_per_page' => $items_per_page,
            'lazy_load_animation' => $animation,
            'lazy_load_scroll_distance' => $scroll_distance
        )
    ));
    ?>
    
    <!-- Elegant Assets Section -->
    <div class="elegant-auction-assets">
        <!-- Section Header -->
        <div class="assets-section-header">
            <h2 class="assets-main-title">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9,22 9,12 15,12 15,22"></polyline>
                </svg>
                <?php _e('الأصول المعروضة', 'auction-system'); ?>
            </h2>
            <?php if ($assets_data['total'] > 0): ?>
                <div class="assets-count-badge">
                    <span class="count-number"><?php echo $assets_data['total']; ?></span>
                    <span class="count-label"><?php _e('عقار', 'auction-system'); ?></span>
                </div>
            <?php endif; ?>
        </div>

        <?php
        // Include the asset filters form
        $filters_form_path = get_stylesheet_directory() . '/inc/auction-components/asset-filters-form.php';
        if (file_exists($filters_form_path)) {
            require_once $filters_form_path;
        }
        ?>

        <!-- Assets Grid -->
        <div class="elegant-assets-grid" id="auction-assets-grid" data-total-items="<?php echo $assets_data['total']; ?>" data-items-per-page="<?php echo $items_per_page; ?>">
            <?php 
            // Display only the initial paginated assets
            if (!empty($assets_data['items'])): 
                foreach ($assets_data['items'] as $index => $asset): 
                    echo get_unified_asset_card($asset, $index);
                endforeach; 
            else: 
                // No Assets State
                ?>
                <div class="elegant-no-assets">
                    <div class="no-assets-icon">
                        <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9,22 9,12 15,12 15,22"></polyline>
                        </svg>
                    </div>
                    <h4><?php _e('لا توجد أصول', 'auction-system'); ?></h4>
                    <p><?php _e('لم يتم إضافة أي أصول عقارية لهذا المزاد بعد.', 'auction-system'); ?></p>
                </div>
            <?php 
            endif; 
            ?>
        </div>
        
        <!-- Load More Button (shown if there are more pages to load) -->
        <?php if ($assets_data['pages'] > 1 && !$auto_load): ?>
            <div class="load-more-container" style="text-align: center; margin: 20px 0;">
                <button id="load-more-assets" class="btn btn-primary">
                    <?php _e('تحميل المزيد', 'auction-system'); ?>
                </button>
            </div>
        <?php endif; ?>
        
        <!-- Loading Indicator -->
        <div id="loading-indicator" style="display: none; text-align: center; padding: 20px;">
            <div class="spinner" style="width: 40px; height: 40px; margin: 0 auto;">
                <svg viewBox="0 0 50 50" style="width: 100%; height: 100%;">
                    <circle cx="25" cy="25" r="20" fill="none" stroke="#3498db" stroke-width="4"></circle>
                </svg>
            </div>
            <p><?php _e('جاري التحميل...', 'auction-system'); ?></p>
        </div>
    </div>
    <?php
}

/**
 * AJAX handler for loading more assets
 */
add_action('wp_ajax_load_more_assets', 'ajax_load_more_assets');
add_action('wp_ajax_nopriv_load_more_assets', 'ajax_load_more_assets');

function ajax_load_more_assets() {
    // Verify nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'load_more_assets')) {
        wp_send_json_error('Invalid nonce');
    }
    
    // Get request parameters
    $auction_id = isset($_POST['auction_id']) ? intval($_POST['auction_id']) : 0;
    $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
    $per_page = isset($_POST['per_page']) ? intval($_POST['per_page']) : 10;
    
    if ($auction_id <= 0) {
        wp_send_json_error('Invalid auction ID');
    }
    
    // Get paginated assets
    $assets_data = get_auction_assets_paginated($auction_id, $page, $per_page);
    
    // Generate HTML for assets
    $html = '';
    if (!empty($assets_data['items'])) {
        foreach ($assets_data['items'] as $index => $asset) {
            $html .= get_unified_asset_card($asset, (($page - 1) * $per_page) + $index);
        }
    }
    
    // Return response
    wp_send_json_success(array(
        'html' => $html,
        'has_more' => $page < $assets_data['pages'],
        'current_page' => $page,
        'total_pages' => $assets_data['pages']
    ));
}
