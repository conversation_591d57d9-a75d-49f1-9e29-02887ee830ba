<?php
/**
 * Auction Ad Spaces Component
 *
 * @package AuctionSystem
 * @version 1.1.0
 * @description مكون مساحات الإعلانات في صفحة المزاد
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display top banner ad space
 * Shows the ad at the top of the auction page
 */
if (!function_exists('auction_display_top_banner_ad')) {
    function auction_display_top_banner_ad($auction_data) {
        global $wpdb;
        
        // Debug output - visible even if no ad content
        echo '<!-- DEBUG: Starting top_banner ad display function -->';
        
        // Direct DB check for debugging
        echo '<!-- DEBUG: Direct DB check for ad settings -->';
        $global_enabled = $wpdb->get_var("SELECT setting_value FROM {$wpdb->prefix}auction_settings WHERE setting_name = 'new_ads_enabled'");
        echo '<!-- DEBUG: Global ads enabled (direct check): ' . $global_enabled . ' -->';
        
        $top_banner_enabled = $wpdb->get_var("SELECT setting_value FROM {$wpdb->prefix}auction_settings WHERE setting_name = 'new_ads_top_banner_enabled'");
        echo '<!-- DEBUG: Top banner enabled (direct check): ' . $top_banner_enabled . ' -->';
        
        // Get ad content from data handler
        echo '<!-- DEBUG: Calling get_ad_content for top_banner -->';
        $ad_content = $auction_data->get_ad_content('top_banner');
        
        // Debug info about retrieved content
        echo '<!-- DEBUG: top_banner content length: ' . strlen($ad_content) . ' -->';
        
        // Always show the container for debugging
        echo '<div id="auction-header" class="auction-ad-container auction-ad-top_banner">';
        
        // Show content if exists
        if (!empty($ad_content)) {
            echo $ad_content;
        } else {
            echo '<!-- DEBUG: Ad top_banner is empty -->';
        }
        
        echo '</div>';
    }
}

/**
 * Display content middle ad space
 * Shows the ad in the middle of the auction content
 */
if (!function_exists('auction_display_content_middle_ad')) {
    function auction_display_content_middle_ad($auction_data) {
        // Get the ad content
        $ad_content = $auction_data->get_ad_content('content_middle');
        
        // Only display the container if we have ad content
        if (!empty($ad_content)) {
            echo '<div class="ad-space ad-content-middle">'. $ad_content .'</div>';
        }
    }
}

/**
 * Display sidebar top ad space
 * Shows the ad at the top of the sidebar
 */
if (!function_exists('auction_display_sidebar_top_ad')) {
    function auction_display_sidebar_top_ad($auction_data) {
        // Get the ad content
        $ad_content = $auction_data->get_ad_content('sidebar_top');
        
        // Only display the container if we have ad content
        if (!empty($ad_content)) {
            echo '<div class="ad-space ad-sidebar-top">'. $ad_content .'</div>';
        }
    }
}

/**
 * Display sidebar bottom ad space
 * Shows the ad at the bottom of the sidebar
 */
if (!function_exists('auction_display_sidebar_bottom_ad')) {
    function auction_display_sidebar_bottom_ad($auction_data) {
        // Get the ad content
        $ad_content = $auction_data->get_ad_content('sidebar_bottom');
        
        // Only display the container if we have ad content
        if (!empty($ad_content)) {
            echo '<div class="ad-space ad-sidebar-bottom">'. $ad_content .'</div>';
        }
    }
}

/**
 * Display bottom banner ad space
 * Shows the ad at the bottom of the auction page
 */
if (!function_exists('auction_display_bottom_banner_ad')) {
    function auction_display_bottom_banner_ad($auction_data) {
        // Get the ad content
        $ad_content = $auction_data->get_ad_content('bottom_banner');
        
        // Only display the container if we have ad content
        if (!empty($ad_content)) {
            echo '<div class="ad-space ad-bottom-banner">'. $ad_content .'</div>';
        }
    }
}
