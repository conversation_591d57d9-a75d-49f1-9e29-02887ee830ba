<?php
/**
 * Debug AJAX Actions Registration
 * 
 * This file helps debug if AJAX actions are properly registered
 */

// Include WordPress
require_once('../../../wp-load.php');

// Check if user is logged in and has proper capabilities
if (!is_user_logged_in()) {
    die('You must be logged in to debug this.');
}

if (!current_user_can('edit_posts')) {
    die('You do not have sufficient permissions.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug AJAX Actions</title>
</head>
<body>
    <h1>Debug AJAX Actions Registration</h1>
    
    <h2>WordPress Hooks Status</h2>
    <?php
    global $wp_filter;
    
    echo "<h3>wp_ajax_approve_auction hooks:</h3>";
    if (isset($wp_filter['wp_ajax_approve_auction'])) {
        echo "<pre>";
        print_r($wp_filter['wp_ajax_approve_auction']);
        echo "</pre>";
    } else {
        echo "<p style='color: red;'>No hooks registered for wp_ajax_approve_auction</p>";
    }
    
    echo "<h3>wp_ajax_reject_auction hooks:</h3>";
    if (isset($wp_filter['wp_ajax_reject_auction'])) {
        echo "<pre>";
        print_r($wp_filter['wp_ajax_reject_auction']);
        echo "</pre>";
    } else {
        echo "<p style='color: red;'>No hooks registered for wp_ajax_reject_auction</p>";
    }
    
    echo "<h3>wp_ajax_delete_auction hooks:</h3>";
    if (isset($wp_filter['wp_ajax_delete_auction'])) {
        echo "<pre>";
        print_r($wp_filter['wp_ajax_delete_auction']);
        echo "</pre>";
    } else {
        echo "<p style='color: red;'>No hooks registered for wp_ajax_delete_auction</p>";
    }
    ?>
    
    <h2>Class Status</h2>
    <?php
    echo "<h3>AuctionAdminDashboard class:</h3>";
    if (class_exists('AuctionAdminDashboard')) {
        echo "<p style='color: green;'>✓ AuctionAdminDashboard class exists</p>";
        
        $reflection = new ReflectionClass('AuctionAdminDashboard');
        echo "<h4>Methods:</h4>";
        echo "<ul>";
        foreach ($reflection->getMethods() as $method) {
            echo "<li>" . $method->getName() . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>✗ AuctionAdminDashboard class does not exist</p>";
    }
    ?>
    
    <h2>Constants</h2>
    <?php
    echo "<p>AUCTION_PLUGIN_PATH: " . (defined('AUCTION_PLUGIN_PATH') ? AUCTION_PLUGIN_PATH : 'Not defined') . "</p>";
    echo "<p>AUCTION_PLUGIN_URL: " . (defined('AUCTION_PLUGIN_URL') ? AUCTION_PLUGIN_URL : 'Not defined') . "</p>";
    ?>
    
    <h2>File Existence</h2>
    <?php
    $files_to_check = [
        'inc/auction-admin-dashboard.php',
        'inc/auction-init.php',
        'functions.php'
    ];
    
    foreach ($files_to_check as $file) {
        $full_path = get_stylesheet_directory() . '/' . $file;
        if (file_exists($full_path)) {
            echo "<p style='color: green;'>✓ $file exists</p>";
        } else {
            echo "<p style='color: red;'>✗ $file does not exist</p>";
        }
    }
    ?>
    
    <h2>Current User Capabilities</h2>
    <?php
    $current_user = wp_get_current_user();
    echo "<p>User: " . $current_user->user_login . "</p>";
    echo "<p>Roles: " . implode(', ', $current_user->roles) . "</p>";
    echo "<p>Can edit_posts: " . (current_user_can('edit_posts') ? 'Yes' : 'No') . "</p>";
    echo "<p>Can manage_options: " . (current_user_can('manage_options') ? 'Yes' : 'No') . "</p>";
    ?>
    
</body>
</html>
