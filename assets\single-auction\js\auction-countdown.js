/**
 * Single Auction Page - Countdown Timer
 *
 * @package AuctionSystem
 * @version 1.0.0
 * @description إدارة العد التنازلي لانتهاء المزاد
 */

(function($) {
    'use strict';

    /**
     * Initialize countdown timer
     */
    function initializeCountdown() {
        $('.countdown').each(function() {
            const $countdown = $(this);
            const targetTime = parseInt($countdown.data('target')) * 1000;

            if (!targetTime) return;

            function updateCountdown() {
                const now = new Date().getTime();
                const distance = targetTime - now;

                if (distance < 0) {
                    $countdown.text('انتهى المزاد');
                    return;
                }

                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                let countdownText = '';

                if (days > 0) {
                    countdownText += days + ' يوم ';
                }
                if (hours > 0) {
                    countdownText += hours + ' ساعة ';
                }
                if (minutes > 0) {
                    countdownText += minutes + ' دقيقة ';
                }
                if (seconds > 0 && days === 0) {
                    countdownText += seconds + ' ثانية';
                }

                $countdown.text(countdownText.trim());
            }

            // Update immediately and then every second
            updateCountdown();
            setInterval(updateCountdown, 1000);
        });
    }

    // Make functions globally available
    window.AuctionCountdown = {
        initializeCountdown: initializeCountdown
    };



})(jQuery);
