<?php
/**
 * دوال عرض الإعلانات في الفرونت إند
 *
 * @package AuctionSystem
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * عرض البانر العلوي
 */
if (!function_exists('auction_display_top_banner_ad')) {
    function auction_display_top_banner_ad($auction_data = null) {
        auction_display_ad_new('top_banner', $auction_data);
    }
}

/**
 * عرض إعلان وسط المحتوى
 */
if (!function_exists('auction_display_content_middle_ad')) {
    function auction_display_content_middle_ad($auction_data = null) {
        auction_display_ad_new('content_middle', $auction_data);
    }
}

/**
 * عرض البانر السفلي
 */
if (!function_exists('auction_display_bottom_banner_ad')) {
    function auction_display_bottom_banner_ad($auction_data = null) {
        auction_display_ad_new('bottom_banner', $auction_data);
    }
}

/**
 * عرض إعلان الشريط الجانبي
 */
if (!function_exists('auction_display_sidebar_ad')) {
    function auction_display_sidebar_ad($auction_data = null) {
        auction_display_ad_new('sidebar', $auction_data);
    }
}

/**
 * عرض إعلان الشريط الجانبي العلوي
 */
if (!function_exists('auction_display_sidebar_top_ad')) {
    function auction_display_sidebar_top_ad($auction_data = null) {
        auction_display_ad_new('sidebar_top', $auction_data);
    }
}

/**
 * عرض إعلان الشريط الجانبي السفلي
 */
if (!function_exists('auction_display_sidebar_bottom_ad')) {
    function auction_display_sidebar_bottom_ad($auction_data = null) {
        auction_display_ad_new('sidebar_bottom', $auction_data);
    }
}

/**
 * الدالة الرئيسية لعرض الإعلانات (اسم جديد لتجنب التضارب)
 */
if (!function_exists('auction_display_ad_new')) {
    function auction_display_ad_new($position, $auction_data = null) {
        // التحقق من تفعيل نظام الإعلانات
        if (!auction_ads_enabled()) {
            return;
        }
        
        // التحقق من تفعيل هذا الموضع
        if (!auction_ad_position_enabled($position)) {
            return;
        }
        // جلب إعدادات الإعلان
        $ad_settings = auction_get_ad_settings($position);
        if (empty($ad_settings) || empty($ad_settings['content'])) {
            return;
        }
        
        // عرض الإعلان حسب النوع
        $ad_html = '';
        
        switch ($ad_settings['type']) {
            case 'google_ads':
                $ad_html = auction_render_google_ad($ad_settings);
                break;
                
            case 'html':
                $ad_html = auction_render_html_ad($ad_settings);
                break;
                
            case 'image':
                $ad_html = auction_render_image_ad($ad_settings);
                break;
        }
        
        if (!empty($ad_html)) {
            echo '<div class="auction-ad-container auction-ad-' . esc_attr($position) . '">';
            echo $ad_html;
            echo '</div>';
        }
    }
}

/**
 * التحقق من تفعيل نظام الإعلانات
 */
if (!function_exists('auction_ads_enabled')) {
    function auction_ads_enabled() {
        if (class_exists('AuctionSettings')) {
            $settings = AuctionSettings::get_instance();
            return $settings->get_setting('new_ads_enabled', '0') === '1';
        }
        return false;
    }
}

/**
 * التحقق من تفعيل موضع إعلان معين
 */
if (!function_exists('auction_ad_position_enabled')) {
    function auction_ad_position_enabled($position) {
        if (class_exists('AuctionSettings')) {
            $settings = AuctionSettings::get_instance();
            return $settings->get_setting("new_ads_{$position}_enabled", '0') === '1';
        }
        return false;
    }
}

/**
 * جلب إعدادات إعلان معين
 */
if (!function_exists('auction_get_ad_settings')) {
    function auction_get_ad_settings($position) {
        if (!class_exists('AuctionSettings')) {
            return array();
        }
        
        $settings = AuctionSettings::get_instance();
        $prefix = "new_ads_{$position}";
        
        // إضافة تعليقات تصحيح للمفاتيح
        echo "<!-- DEBUG: auction_get_ad_settings للموضع: {$position} -->\n";
        echo "<!-- المفاتيح المستخدمة: {$prefix}_* -->\n";
        
        // جلب نوع الإعلان
        $type = $settings->get_setting("{$prefix}_type", 'html');
        echo "<!-- نوع الإعلان: {$type} -->\n";
        
        // جلب محتوى الإعلان
        $content = $settings->get_setting("{$prefix}_content", '');
        echo "<!-- محتوى الإعلان: " . (empty($content) ? 'فارغ' : 'موجود - ' . strlen($content) . ' حرف') . " -->\n";
        
        // جلب رابط الإعلان للصور
        $url = $settings->get_setting("{$prefix}_image_link_url", '');
        echo "<!-- رابط الصورة: {$url} -->\n";
        
        // جلب النص البديل للصور
        $alt = $settings->get_setting("{$prefix}_image_alt", '');
        
        // إذا كان نوع الإعلان صورة ولكن لا يوجد محتوى، فحاول البحث عن رابط الصورة
        if ($type === 'image' && empty($content)) {
            $image_url = $settings->get_setting("{$prefix}_image_url", '');
            $content = $image_url;
            echo "<!-- رابط الصورة: {$image_url} -->\n";
        }
        
        // للتصحيح فقط - إنشاء محتوى اختباري إذا لم يتم العثور على محتوى
        if (empty($content)) {
            echo "<!-- لم يتم العثور على محتوى، إنشاء محتوى اختباري للتصحيح -->\n";
            $content = '<div style="background:#f0ad4e; color:#fff; text-align:center; padding:10px; margin:10px 0; border:1px solid #eea236;">';
            $content .= '<strong>' . ucfirst(str_replace('_', ' ', $position)) . ' اختبار إعلان</strong> - هذا إعلان مؤقت للتصحيح فقط.';
            $content .= '</div>';
        }
        
        return array(
            'type' => $type,
            'content' => $content,
            'url' => $url,
            'alt' => $alt
        );
    }
}

/**
 * عرض إعلان Google
 */
if (!function_exists('auction_render_google_ad')) {
    function auction_render_google_ad($settings) {
        if (empty($settings['content'])) {
            return '';
        }
        
        return '<div class="auction-google-ad">' . $settings['content'] . '</div>';
    }
}

/**
 * عرض إعلان HTML مخصص
 */
if (!function_exists('auction_render_html_ad')) {
    function auction_render_html_ad($settings) {
        if (empty($settings['content'])) {
            return '';
        }
        
        return '<div class="auction-html-ad">' . $settings['content'] . '</div>';
    }
}

/**
 * عرض إعلان صورة
 */
if (!function_exists('auction_render_image_ad')) {
    function auction_render_image_ad($settings) {
        if (empty($settings['content'])) {
            return '';
        }
        
        $image_url = esc_url($settings['content']);
        $link_url = !empty($settings['url']) ? esc_url($settings['url']) : '';
        $alt_text = !empty($settings['alt']) ? esc_attr($settings['alt']) : 'إعلان';
        
        $html = '<div class="auction-image-ad">';
        
        if (!empty($link_url)) {
            $html .= '<a href="' . $link_url . '" target="_blank" rel="noopener">';
        }
        
        $html .= '<img src="' . $image_url . '" alt="' . $alt_text . '" class="auction-ad-image" />';
        
        if (!empty($link_url)) {
            $html .= '</a>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
}

/**
 * تحديث دالة عرض الشريط الجانبي لعرض معلومات الشركة والإعلانات
 */
function auction_display_sidebar($auction_details, $auction_data) {
    if (!$auction_details) {
        return;
    }
    
    // التأكد من وجود الدوال المطلوبة
    $company_info_exists = function_exists('auction_display_company_info');
    $contact_card_exists = function_exists('auction_display_contact_card');
    
    // إظهار قسم الشريط الجانبي
    echo '<div class="auction-sidebar">';
       // عرض إعلان الشريط الجانبي العلوي
       if (function_exists('auction_ad_position_enabled') && auction_ad_position_enabled('sidebar_top')) {
        echo '<div class="sidebar-ad-container sidebar-top-ad">';
        echo '<h3>' . __('رعاة المنصة', 'auction-system') . '</h3>';
        // التحقق من وجود دالة العرض
        if (function_exists('auction_display_sidebar_top_ad')) {
            auction_display_sidebar_top_ad($auction_data);
        } else {
            // استخدام الدالة البديلة إذا كانت الدالة المخصصة غير موجودة
            auction_display_ad_new('sidebar_top', $auction_data);
        }
        echo '</div>';
    }
    // عرض معلومات الشركة إذا كانت الدالة موجودة
    if ($company_info_exists) {
        auction_display_company_info($auction_details);
    } else {
        // عرض معلومات الشركة بشكل بسيط إذا لم تكن الدالة موجودة
        if (!empty($auction_details['company_name'])) {
            echo '<div class="info-card company-info-basic">';
            echo '<h3>' . __('معلومات الشركة', 'auction-system') . '</h3>';
            echo '<div class="company-details">';
            echo '<h4>' . esc_html($auction_details['company_name']) . '</h4>';
            
            if (!empty($auction_details['company_license'])) {
                echo '<p><strong>' . __('ترخيص رقم:', 'auction-system') . '</strong> ' . esc_html($auction_details['company_license']) . '</p>';
            }
            
            if (!empty($auction_details['company_phone'])) {
                echo '<p><strong>' . __('الهاتف:', 'auction-system') . '</strong> <a href="tel:' . esc_attr($auction_details['company_phone']) . '">' . esc_html($auction_details['company_phone']) . '</a></p>';
            }
            
            if (!empty($auction_details['company_email'])) {
                echo '<p><strong>' . __('البريد الإلكتروني:', 'auction-system') . '</strong> <a href="mailto:' . esc_attr($auction_details['company_email']) . '">' . esc_html($auction_details['company_email']) . '</a></p>';
            }
            
            if (!empty($auction_details['company_address'])) {
                echo '<p><strong>' . __('العنوان:', 'auction-system') . '</strong> ' . esc_html($auction_details['company_address']) . '</p>';
            }
            
            if (!empty($auction_details['company_website'])) {
                echo '<p><strong>' . __('الموقع الإلكتروني:', 'auction-system') . '</strong> <a href="' . esc_url($auction_details['company_website']) . '" target="_blank" rel="noopener noreferrer">' . esc_html($auction_details['company_website']) . '</a></p>';
            }
            
            echo '</div>'; // .company-details
            echo '</div>'; // .info-card
        }
    }
    
    // عرض بطاقة الاتصال إذا كانت الدالة موجودة
    if ($contact_card_exists) {
        auction_display_contact_card($auction_details);
    }
    
 
    
    // عرض إعلان الشريط الجانبي السفلي
    if (function_exists('auction_ad_position_enabled') && auction_ad_position_enabled('sidebar_bottom')) {
        echo '<div class="sidebar-ad-container sidebar-bottom-ad">';
        echo '<h3>' . __('رعاة المنصة', 'auction-system') . '</h3>';
        // التحقق من وجود دالة العرض
        if (function_exists('auction_display_sidebar_bottom_ad')) {
            auction_display_sidebar_bottom_ad($auction_data);
        } else {
            // استخدام الدالة البديلة إذا كانت الدالة المخصصة غير موجودة
            auction_display_ad_new('sidebar_bottom', $auction_data);
        }
        echo '</div>';
    }
    
    echo '</div>'; // .auction-sidebar
    
    // إضافة أنماط CSS للشريط الجانبي
    echo '<style>
        .auction-sidebar {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .info-card {
            background: #fff;
            border-radius: 6px;
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .info-card h3 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            color: #333;
            font-size: 18px;
        }
        .company-info-basic h4 {
            color: #2c3e50;
            margin: 0 0 15px 0;
        }
        .company-details p {
            margin: 8px 0;
            line-height: 1.5;
        }
        .company-details a {
            color: #3498db;
            text-decoration: none;
        }
        .company-details a:hover {
            text-decoration: underline;
        }
        .sidebar-ad-container {
            background: #fff;
            border-radius: 6px;
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .sidebar-ad-container h3 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            color: #333;
            font-size: 18px;
            text-align: center;
        }
    </style>';
}