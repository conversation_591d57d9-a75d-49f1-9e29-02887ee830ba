<?php
/**
 * Asset Filters Form Template
 *
 * @package AuctionSystem
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}
?>
<div class="asset-filters-container" id="asset-filters">
    <form id="asset-filters-form" class="asset-filters-form">
        <div class="filter-fields-grid">
            <div class="filter-field filter-field-deed-number">
                <label for="filter_deed_number"><?php _e('رقم الصك', 'auction-system'); ?></label>
                <input type="text" id="filter_deed_number" name="filter_deed_number" class="filter-input" placeholder="<?php esc_attr_e('أدخل رقم الصك', 'auction-system'); ?>">
            </div>

            <div class="filter-field filter-field-asset-type">
                <label for="filter_asset_type_id"><?php _e('نوع الأصل', 'auction-system'); ?></label>
                <select id="filter_asset_type_id" name="filter_asset_type_id" class="filter-select">
                    <option value=""><?php _e('الكل', 'auction-system'); ?></option>
                    <?php
                    if (class_exists('AuctionAssetTypes')) {
                        // Ensure the class is loaded. You might need to require_once the file if it's not auto-loaded.
                        // require_once get_stylesheet_directory() . '/inc/auction-asset-types.php'; // If not already included
                        
                        $asset_type_manager = new AuctionAssetTypes();
                        // Check if the method is public or if there's a static way to get types
                        // Assuming get_asset_types() is public or there's a static helper.
                        // If get_asset_types is private, we might need a public static helper in AuctionAssetTypes class.
                        // For now, let's assume it's accessible or we'll adjust.
                        // A better approach might be a dedicated public static function like AuctionAssetTypes::get_all_active_types()
                        
                        // Let's try to call it directly. If it's private, this will fail and we'll need to adjust.
                        // This is a common pattern, but the actual implementation of get_asset_types might differ.
                        // We need a way to get $asset_types similar to how it's done in the render() method of the class.
                        // For now, let's assume a helper function or direct call works.
                        // This part might need adjustment based on how AuctionAssetTypes is structured for external calls.

                        // A more robust way if get_asset_types is not directly callable or static:
                        // You might need a helper function in your theme or in AuctionAssetTypes class itself.
                        // For example, in AuctionAssetTypes class, add:
                        // public static function get_all_active_types_for_select() {
                        //     global $wpdb;
                        //     $table_name = $wpdb->prefix . 'auction_asset_types'; // Assuming this is the table name
                        //     return $wpdb->get_results("SELECT id, name FROM {$table_name} WHERE is_active = 1 ORDER BY sort_order ASC, name ASC");
                        // }
                        // Then call: $asset_types_list = AuctionAssetTypes::get_all_active_types_for_select();

                        // For this example, I'll simulate fetching them if the class method isn't directly usable here.
                        // This is a placeholder for the actual data fetching logic.
                        global $wpdb;
                        $asset_types_table = $wpdb->prefix . 'auction_asset_types'; // Assuming table name
                        if ($wpdb->get_var("SHOW TABLES LIKE '$asset_types_table'") == $asset_types_table) {
                             $asset_types_list = $wpdb->get_results("SELECT id, name FROM {$asset_types_table} WHERE is_active = 1 ORDER BY sort_order ASC, name ASC");
                             if ($asset_types_list) {
                                foreach ($asset_types_list as $type) {
                                    echo '<option value="' . esc_attr($type->id) . '">' . esc_html($type->name) . '</option>';
                                }
                            }
                        } else {
                            // Fallback if table doesn't exist or no types found
                            echo '<option value="" disabled>' . __('لم يتم العثور على أنواع أصول', 'auction-system') . '</option>';
                        }
                    } else {
                        // Fallback if class AuctionAssetTypes doesn't exist
                        echo '<option value="1">' . __('عقار سكني (مثال)', 'auction-system') . '</option>';
                        echo '<option value="2">' . __('عقار تجاري (مثال)', 'auction-system') . '</option>';
                        echo '<option value="3">' . __('أرض (مثال)', 'auction-system') . '</option>';
                    }
                    ?>
                </select>
            </div>

            <div class="filter-field filter-field-district">
                <label for="filter_district"><?php _e('الحي', 'auction-system'); ?></label>
                <input type="text" id="filter_district" name="filter_district" class="filter-input" placeholder="<?php esc_attr_e('أدخل اسم الحي', 'auction-system'); ?>">
            </div>

            <div class="filter-field filter-field-area filter-field-large">
                <label><?php _e('المساحة (م²)', 'auction-system'); ?></label>
                <div class="area-range-slider-container">
                    <div id="area-range-slider"></div>
                    <div class="area-range-values">
                        <span id="area-min-value-display">0</span> م² - <span id="area-max-value-display">10000</span> م²
                    </div>
                    <input type="hidden" id="filter_area_min" name="filter_area_min" value="0">
                    <input type="hidden" id="filter_area_max" name="filter_area_max" value="10000">
                </div>
            </div>
        </div>

        <div class="filter-actions">
            <button type="submit" id="apply-asset-filters" class="btn btn-primary filter-button">
                <i class="fas fa-search" style="margin-left: 5px;"></i> <?php _e('بحث', 'auction-system'); ?>
            </button>
            <button type="button" id="reset-asset-filters" class="btn btn-secondary filter-button reset-button">
                <i class="fas fa-undo" style="margin-left: 5px;"></i> <?php _e('إعادة تعيين', 'auction-system'); ?>
            </button>
        </div>
    </form>
</div>