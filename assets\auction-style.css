/* ========================================
   AUCTION SYSTEM - MAIN STYLESHEET
   ======================================== */

/**
 * Auction System Styles
 *
 * @package AuctionSystem
 * @version 2.0.0
 */

/* Reset and Base Styles */
* {
    box-sizing: border-box;
}

/* CSS Variables - Color System */
:root {
    /* Primary Colors */
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --primary-light: #7c8aed;

    /* Secondary Colors */
    --secondary-color: #764ba2;
    --secondary-dark: #6b4190;
    --secondary-light: #8659b4;

    /* Accent Colors */
    --accent-blue: #3498db;
    --accent-blue-dark: #2980b9;
    --accent-green: #27ae60;
    --accent-red: #e74c3c;
    --accent-orange: #f39c12;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Text Colors */
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-muted: #95a5a6;
    --text-white: #ffffff;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-dark: #1e293b;

    /* Border Colors */
    --border-light: #e1e8ed;
    --border-medium: #cbd5e1;
    --border-dark: #94a3b8;

    /* Shadow Colors */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    --gradient-blue: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-blue-dark) 100%);

    /* Spacing */
    --spacing-xs: 0.25rem;   /* 4px */
    --spacing-sm: 0.5rem;    /* 8px */
    --spacing-md: 1rem;      /* 16px */
    --spacing-lg: 1.5rem;    /* 24px */
    --spacing-xl: 2rem;      /* 32px */
    --spacing-2xl: 3rem;     /* 48px */

    /* Border Radius */
    --radius-sm: 0.25rem;    /* 4px */
    --radius-md: 0.5rem;     /* 8px */
    --radius-lg: 0.75rem;    /* 12px */
    --radius-xl: 1rem;       /* 16px */

    /* Typography */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;   /* 12px */
    --font-size-sm: 0.875rem;  /* 14px */
    --font-size-base: 1rem;    /* 16px */
    --font-size-lg: 1.125rem;  /* 18px */
    --font-size-xl: 1.25rem;   /* 20px */
    --font-size-2xl: 1.5rem;   /* 24px */
    --font-size-3xl: 1.875rem; /* 30px */
    --font-size-4xl: 2.25rem;  /* 36px */

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Base Styles */
.auction-create-page,
.auction-single-page {
    font-family: var(--font-family);
    direction: rtl;
    text-align: right;
    line-height: 1.6;
    color: var(--text-primary);
    width: 100%;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: 400; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-white { color: var(--text-white); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-gradient { background: var(--gradient-primary); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.transition { transition: all var(--transition-normal); }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }

/* Spacing Utilities */
.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }
.p-6 { padding: var(--spacing-2xl); }

.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }
.m-6 { margin: var(--spacing-2xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }
.mb-6 { margin-bottom: var(--spacing-2xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }
.mt-6 { margin-top: var(--spacing-2xl); }

/* Display Utilities */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.gap-1 { gap: var(--spacing-xs); }
.gap-2 { gap: var(--spacing-sm); }
.gap-3 { gap: var(--spacing-md); }
.gap-4 { gap: var(--spacing-lg); }
.gap-5 { gap: var(--spacing-xl); }
.gap-6 { gap: var(--spacing-2xl); }

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.scale-in {
    animation: scaleIn 0.4s ease-out;
}

@keyframes scaleIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-200);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Advertisement Styles */
.auction-ad {
    margin: var(--spacing-lg) 0;
    text-align: center;
}

.auction-ad-top_banner {
    margin-bottom: var(--spacing-xl);
}

.auction-ad-sidebar {
    margin-bottom: var(--spacing-lg);
}

.auction-ad-bottom_banner {
    margin-top: var(--spacing-xl);
}

.auction-ad img {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
}

.auction-ad img:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .md\:hidden { display: none; }
    .md\:block { display: block; }
    .md\:flex { display: flex; }
    .md\:grid { display: grid; }
}

@media (max-width: 480px) {
    .sm\:hidden { display: none; }
    .sm\:block { display: block; }
    .sm\:flex { display: flex; }
    .sm\:text-center { text-align: center; }
}

/* ========================================
   AUCTION FORM STYLES
   ======================================== */

/* Page Layout */
.auction-container {
    min-height: 100vh;
    width: 90%;
}

.auction-container .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    width: 100%;
}

/* Full width override for edit pages */


body.page-template-page-edit-auction .auction-container .container {
    max-width: 1400px;
    padding: 0 40px;
}

.page-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.page-header h1 {
    font-size: var(--font-size-3xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: 700;
}

.page-description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Form Container */
.auction-form-container {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    max-width: 900px;
    margin: 0 auto;
}

/* Wider form container for edit pages */
body.page-template-page-edit-auction .auction-form-container {
    max-width: 1200px;
}

/* Progress Steps */
.form-progress {
    background: var(--gradient-primary);
    padding: var(--spacing-xl);
    color: var(--white);
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    transition: var(--transition-normal);
}

.step.active .step-number {
    background: var(--white);
    color: var(--primary-color);
}

.step.completed .step-number {
    background: var(--accent-green);
    color: var(--white);
}

.step-title {
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

.step.active .step-title {
    opacity: 1;
    font-weight: 600;
}

.progress-bar {
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--white);
    transition: width var(--transition-slow);
    border-radius: 2px;
}

/* Form Steps */
.form-step {
    display: none;
    padding: var(--spacing-2xl);
}

.form-step.active {
    display: block;
}

.step-content h3 {
    font-size: var(--font-size-2xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xl);
    text-align: center;
    font-weight: 600;
}

/* Form Elements */
.form-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-row.two-cols {
    grid-template-columns: 1fr 1fr;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-base);
}

.form-group label.required::after {
    content: ' *';
    color: var(--accent-red);
}

.form-control {
    padding: var(--spacing-md);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    transition: var(--transition-normal);
    background: var(--white);
    color: var(--text-primary);
    font-family: var(--font-family);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control.error {
    border-color: var(--accent-red);
}

.error-message {
    color: var(--accent-red);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: none;
}

.error-message.show {
    display: block;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    font-family: var(--font-family);
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--text-primary);
    border: 1px solid var(--border-medium);
}

.btn-secondary:hover {
    background: var(--gray-200);
}

.btn-success {
    background: var(--accent-green);
    color: var(--white);
}

.btn-success:hover {
    background: #219a52;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Form Navigation */
.form-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-2xl);
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--border-light);
}

.submit-buttons {
    display: flex;
    gap: var(--spacing-md);
}

/* Upload Areas */
.file-upload-area,
.brochure-upload-area {
    border: 2px dashed var(--border-medium);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    cursor: pointer;
    transition: var(--transition-normal);
    background: var(--bg-secondary);
}

.file-upload-area:hover,
.brochure-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

.upload-icon {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

.upload-text {
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
}

.upload-hint {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.file-upload-area.dragover,
.brochure-upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.02);
}

/* File Preview */
.file-preview {
    margin-top: var(--spacing-md);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
}

.file-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.file-icon {
    color: var(--accent-red);
    flex-shrink: 0;
}

.file-details {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.file-size {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.remove-file {
    background: var(--accent-red);
    color: var(--white);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
}

.remove-file:hover {
    background: #c0392b;
    transform: scale(1.1);
}

/* Asset Management */
.assets-empty {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.asset-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-xl);
    overflow: hidden;
}

.asset-header {
    background: var(--gradient-primary);
    color: var(--white);
    padding: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.asset-header h4 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.remove-asset {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.remove-asset:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.asset-content {
    padding: var(--spacing-xl);
}

.asset-images-area {
    margin-bottom: var(--spacing-md);
}

.images-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.image-item {
    position: relative;
    border-radius: var(--radius-md);
    overflow: hidden;
    aspect-ratio: 1;
    background: var(--gray-100);
}

.image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.remove-image {
    position: absolute;
    top: var(--spacing-xs);
    right: var(--spacing-xs);
    background: var(--accent-red);
    color: var(--white);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
    opacity: 0.9;
}

.remove-image:hover {
    opacity: 1;
    transform: scale(1.1);
}

.add-asset-section {
    text-align: center;
    margin-top: var(--spacing-xl);
}

/* Google Maps Preview */
.map-preview {
    margin-top: var(--spacing-md);
}

.map-container {
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--white);
    box-shadow: var(--shadow-sm);
}

.map-header {
    background: var(--gradient-primary);
    color: var(--white);
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
}

.map-icon {
    font-size: var(--font-size-lg);
}

.map-title {
    font-size: var(--font-size-base);
}

.map-content {
    position: relative;
}

.coordinates-info {
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    position: absolute;
    bottom: var(--spacing-sm);
    left: var(--spacing-sm);
    z-index: 10;
}

.map-footer {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.map-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--primary-color);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 600;
    transition: var(--transition-normal);
}

.map-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.map-provider {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    font-style: italic;
}

/* Loading Animation */
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Success/Error Messages */
.auction-success-message {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    text-align: center;
    margin: var(--spacing-xl) 0;
}

.auction-error-message {
    padding: var(--spacing-xl) 0;
    text-align: center;
}

.auction-error-message {
    background: var(--accent-red);
    color: var(--white);
    margin-bottom: var(--spacing-xl);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
}

.success-content,
.error-content {
    max-width: 600px;
    margin: 0 auto;
}

.success-icon {
    margin-bottom: var(--spacing-lg);
}

.success-content h2 {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-md);
    font-weight: 700;
}

.success-content p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
    line-height: 1.6;
}

.success-details {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin: var(--spacing-xl) 0;
    text-align: right;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-base);
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-icon {
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.detail-text {
    flex: 1;
    opacity: 0.95;
}

.success-actions {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

/* Review Section */
.review-section {
    display: grid;
    gap: var(--spacing-lg);
}

.review-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-light);
}

.review-card h4 {
    font-size: var(--font-size-xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    font-weight: 600;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: var(--spacing-sm);
}

.review-content {
    color: var(--text-secondary);
    line-height: 1.6;
}

.review-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-light);
}

.review-item:last-child {
    border-bottom: none;
}

.review-label {
    font-weight: 600;
    color: var(--text-primary);
    min-width: 120px;
    flex-shrink: 0;
}

.review-value {
    color: var(--text-secondary);
    text-align: right;
    flex: 1;
    margin-right: var(--spacing-md);
}

.review-value a {
    color: var(--primary-color);
    text-decoration: none;
}

.review-value a:hover {
    text-decoration: underline;
}

.asset-review-item {
    background: var(--white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.asset-review-item h5 {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
}

.no-data {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: var(--spacing-xl);
}

/* Terms Section */
.terms-section {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-light);
}

.checkbox-container {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    cursor: pointer;
    font-size: var(--font-size-base);
    line-height: 1.6;
}

.checkbox-container input[type="checkbox"] {
    margin: 0;
    width: 20px;
    height: 20px;
    accent-color: var(--primary-color);
}

/* Terms section error state */
.terms-section.error {
    border-color: var(--accent-red);
    background-color: rgba(220, 53, 69, 0.05);
}

.terms-section.error .checkbox-container {
    color: var(--accent-red);
}

/* Brochure upload error state */
.brochure-upload-area.error,
.file-upload-area.error {
    border-color: var(--accent-red) !important;
    background-color: rgba(220, 53, 69, 0.05);
}

.brochure-upload-area.error .upload-text,
.file-upload-area.error .upload-text {
    color: var(--accent-red);
}

/* Auction location map preview */
#auction-map-preview {
    margin-top: 15px;
}

#auction-map-preview .map-container {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background: #f9f9f9;
}

#auction-map-preview .map-header {
    padding: 12px 15px;
    background: #f5f5f5;
    border-bottom: 1px solid #ddd;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #333;
}

#auction-map-preview .map-icon {
    font-size: 16px;
}

#auction-map-preview .map-content {
    position: relative;
}

#auction-map-preview .google-map-container iframe {
    width: 100%;
    height: 250px;
    border: none;
    display: block;
}

#auction-map-preview .coordinates-info {
    padding: 10px 15px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    font-size: 13px;
    color: #666;
    font-family: monospace;
}

#auction-map-preview .map-footer {
    padding: 10px 15px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

#auction-map-preview .map-link {
    color: var(--primary-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
    transition: color 0.2s ease;
}

#auction-map-preview .map-link:hover {
    color: var(--primary-dark);
}

#auction-map-preview .map-provider {
    color: #999;
    font-style: italic;
}

/* File upload area error state */
.file-upload-area.error,
.brochure-upload-area.error {
    border-color: var(--accent-red);
    background-color: rgba(220, 53, 69, 0.05);
}

/* Full Width for Auction Pages */
body.page-template-page-edit-auction .one-container .site-content,
body.page-template-page-create-auction .one-container .site-content {
    max-width: none !important;
    padding: 0 !important;
}

body.page-template-page-edit-auction .content-area,
body.page-template-page-create-auction .content-area {
    width: 100% !important;
    max-width: none !important;
}

body.page-template-page-edit-auction .site-main,
body.page-template-page-create-auction .site-main {
    width: 100% !important;
    max-width: none !important;
    padding: 0 !important;
}

/* Edit Page Specific Styles */
.edit-header {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.edit-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.edit-title svg {
    color: #3498db;
}

.header-actions {
    display: flex;
    gap: 12px;
}

.status-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-publish {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-draft {
    background: #f8d7da;
    color: #721c24;
}

.success-message {
    background: #d4edda;
    color: #155724;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    border: 1px solid #c3e6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auction-create-page {
        padding: var(--spacing-lg) 0;
    }

    .form-step {
        padding: var(--spacing-lg);
    }

    .form-row.two-cols {
        grid-template-columns: 1fr;
    }

    .progress-steps {
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }

    .step {
        min-width: 80px;
    }

    .step-title {
        font-size: var(--font-size-xs);
    }

    .form-navigation {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .submit-buttons {
        width: 100%;
        justify-content: center;
    }

    .success-actions {
        flex-direction: column;
        align-items: center;
    }

    /* Edit page responsive */
    .edit-header {
        padding: 20px;
    }

    .header-content {
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }

    .edit-title {
        font-size: 1.5rem;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .edit-title {
        font-size: 1.3rem;
    }
}

/* ========================================
   END OF MAIN STYLESHEET
   ======================================== */









