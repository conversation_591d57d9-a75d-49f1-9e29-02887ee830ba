/**
 * Edit Auction JavaScript - Extension for auction-script.js
 *
 * @package AuctionSystem
 * @version 1.0.0
 * @description جافا سكريبت إضافي لصفحة تعديل المزاد - يعتمد على auction-script.js
 */

(function($) {
    'use strict';

    // Edit-specific functionality
    const AuctionEditSystem = {
        // Edit mode flag
        isEditMode: true,

        // Initialize edit system
        init: function() {
            // Use existing AuctionSystem if available
            if (window.AuctionSystem) {
                // Extend existing system with edit data
                window.AuctionSystem.isEditMode = true;
                this.loadEditData();

                // Initialize existing system
                window.AuctionSystem.init();
            } else {
                console.warn('AuctionSystem not found. Edit functionality may be limited.');
            }
        },

        // Load edit-specific data
        loadEditData: function() {
            if (typeof auctionEditData !== 'undefined' && window.AuctionSystem) {
                window.AuctionSystem.auctionData = auctionEditData.auction_data;
                window.AuctionSystem.assetTypes = auctionEditData.asset_types;
                window.AuctionSystem.strings = auctionEditData.strings;

                // Pre-populate form with existing data
                this.populateForm();
            }
        },

        // Populate form with existing auction data
        populateForm: function() {
            const data = auctionEditData.auction_data;

            // Populate basic fields
            $('#auction_title').val(data.title || '');
            $('#auction_description').val(data.description || '');
            $('#auction_date').val(data.date || '');
            $('#auction_time').val(data.time || '');
            $('#auction_city').val(data.city || '');
            $('#auction_type').val(data.type || '');

            // Populate company fields
            $('#company_name').val(data.company_name || '');
            $('#company_license').val(data.company_license || '');
            $('#company_phone').val(data.company_phone || '');
            $('#company_email').val(data.company_email || '');
            $('#company_address').val(data.company_address || '');
            $('#company_website').val(data.company_website || '');
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        AuctionEditSystem.init();
    });

    // Export to global scope
    window.AuctionEditSystem = AuctionEditSystem;

})(jQuery);