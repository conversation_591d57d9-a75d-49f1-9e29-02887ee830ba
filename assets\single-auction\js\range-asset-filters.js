/**
 * Asset Filters JavaScript
 *
 * @package AuctionSystem
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        // console.log('Asset Filters JS Loaded');

        // Initialize Range Slider (example using noUiSlider - library needs to be enqueued separately)
        var areaSliderElement = document.getElementById('area-range-slider');
        if (typeof noUiSlider !== 'undefined' && areaSliderElement) {
            var auctionMinArea = 0; // Default min area
            var auctionMaxArea = 50000; // Default max area, adjust as needed from settings or actual data range

            // You might want to get actual min/max from all assets if possible, or set a reasonable default.
            // For now, using fixed defaults.
            if (typeof assetFiltersData !== 'undefined') {
                // Example: if you pass default min/max via wp_localize_script
                // auctionMinArea = assetFiltersData.default_min_area || 0;
                // auctionMaxArea = assetFiltersData.default_max_area || 50000;
            }

            noUiSlider.create(areaSliderElement, {
                start: [
                    parseInt($('#filter_area_min').val()) || auctionMinArea,
                    parseInt($('#filter_area_max').val()) || auctionMaxArea
                ],
                connect: true,
                step: 100, // Adjust step as needed
                tooltips: [true, true], // Show tooltips
                format: {
                    to: function (value) {
                        return parseInt(value);
                    },
                    from: function (value) {
                        return parseInt(value);
                    }
                },
                range: {
                    'min': auctionMinArea,
                    'max': auctionMaxArea
                }
            });

            areaSliderElement.noUiSlider.on('update', function (values, handle) {
                $('#area-min-value-display').text(values[0]);
                $('#filter_area_min').val(values[0]);
                $('#area-max-value-display').text(values[1]);
                $('#filter_area_max').val(values[1]);
            });
        }        // Handle form submission
        $('#asset-filters-form').on('submit', function(e) {
            e.preventDefault();
            performAssetFilter(false); // false for not a reset
        });

        // Handle reset button
        $('#reset-asset-filters').on('click', function() {
            $('#asset-filters-form').trigger('reset');
            if (typeof noUiSlider !== 'undefined' && areaSliderElement && areaSliderElement.noUiSlider) {
                var auctionMinArea = (typeof assetFiltersData !== 'undefined' && assetFiltersData.default_min_area !== undefined) ? assetFiltersData.default_min_area : 0;
                var auctionMaxArea = (typeof assetFiltersData !== 'undefined' && assetFiltersData.default_max_area !== undefined) ? assetFiltersData.default_max_area : 50000;
                areaSliderElement.noUiSlider.set([auctionMinArea, auctionMaxArea]);
            } else {
                var defaultMin = (typeof assetFiltersData !== 'undefined' && assetFiltersData.default_min_area !== undefined) ? assetFiltersData.default_min_area : 0;
                var defaultMax = (typeof assetFiltersData !== 'undefined' && assetFiltersData.default_max_area !== undefined) ? assetFiltersData.default_max_area : 10000;
                $('#filter_area_min').val(defaultMin);
                $('#filter_area_max').val(defaultMax);
                $('#area-min-value-display').text(defaultMin);
                $('#area-max-value-display').text(defaultMax);
            }
            performAssetFilter(true); // true for reset
        });

        function performAssetFilter(isReset) {
            var dataToSend = {};
            
            if (!isReset) {
                var formData = $('#asset-filters-form').serializeArray();
                $(formData).each(function(i, field){
                    dataToSend[field.name] = field.value;
                });
            }

            dataToSend.action = 'filter_auction_assets';
            dataToSend.nonce = assetFiltersData.nonce;
            dataToSend.auction_id = assetFiltersData.auction_id;
            if (isReset) {
                dataToSend.is_reset = '1';
            }

            var $assetsGrid = $('#auction-assets-grid');
            
            $assetsGrid.html('<div class="assets-loading" style="text-align:center; padding: 40px;"><i class="fas fa-spinner fa-spin" style="font-size:24px; margin-bottom:10px;"></i><p>' + assetFiltersData.loading_message + '</p></div>');
            $('#apply-asset-filters').prop('disabled', true).addClass('loading');

            $.ajax({
                url: assetFiltersData.ajax_url,
                type: 'POST',
                data: dataToSend,
                success: function(response) {
                    if (response.success) {
                        if (response.data.html) {
                            $assetsGrid.html(response.data.html);
                        } else {
                            $assetsGrid.html('<div class="assets-no-results" style="text-align:center; padding: 40px;"><i class="fas fa-info-circle" style="font-size:24px; margin-bottom:10px; color: #888;"></i><p>' + assetFiltersData.no_results_message + '</p></div>');
                        }
                        
                        if (typeof response.data.count !== 'undefined') {
                            $('.assets-count-badge .count-number').text(response.data.count);
                        }
                    } else {
                        var errorMessage = response.data && response.data.message ? response.data.message : 'An error occurred.';
                        $assetsGrid.html('<div class="assets-error" style="text-align:center; padding: 40px; color: red;"><i class="fas fa-exclamation-triangle" style="font-size:24px; margin-bottom:10px;"></i><p>' + errorMessage + '</p></div>');
                    }
                },
                error: function() {
                    $assetsGrid.html('<div class="assets-error" style="text-align:center; padding: 40px; color: red;"><i class="fas fa-exclamation-triangle" style="font-size:24px; margin-bottom:10px;"></i><p>Request failed. Please try again.</p></div>');
                },
                complete: function() {
                    $('#apply-asset-filters').prop('disabled', false).removeClass('loading');
                }
            });
        }

        // Initial load
        performAssetFilter(true);
    });
})(jQuery);

