/**
 * Auction Settings Page JavaScript
 * @version 1.0.0
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initializeSettings();
        initializeFormValidation();
        initializeTooltips();
    });

    /**
     * Initialize all settings functionality
     */    function initializeSettings() {
        initializeMediaUploader();
        // initializeToggles(); // تم التعليق أو يمكن إزالته إذا كان يسبب تعارضًا مع مفاتيح تبديل الإعلانات
        // initializeAdSettings(); // تم التعليق - سيتم إعادة بناء منطق الإعلانات
        initializeNewAdSettingsLogic(); // إضافة الدالة الجديدة
        initializeDynamicFields();
        handleDependencies();
    }

    /**
     * التحقق من صحة النموذج
     */
    function initializeFormValidation() {
        $('.auction-settings-form').on('submit', function(e) {
            let isValid = true;
            
            // التحقق من الحقول المطلوبة
            $(this).find('[required]').each(function() {
                if (!$(this).val()) {
                    isValid = false;
                    $(this).addClass('error');
                    showError($(this));
                }
            });

            // التحقق من صحة القيم
            validateNumericFields();
            
            if (!isValid) {
                e.preventDefault();
                showNotification('يرجى تصحيح الأخطاء قبل الحفظ', 'error');
            }
        });
    }

    /**
     * إضافة tooltips للحقول
     */
    function initializeTooltips() {
        $('.setting-label').each(function() {
            const description = $(this).siblings('.setting-description').text();
            if (description) {
                $(this).append(`
                    <span class="tooltip-icon dashicons dashicons-info-outline" 
                          title="${description}">
                    </span>
                `);
            }
        });

        // تهيئة tooltips
        if (typeof $.fn.tooltip === 'function') {
            $('.tooltip-icon').tooltip({
                position: { my: "center bottom", at: "center top-10" }
            });
        }
    }

    /**
     * التعامل مع الحقول الديناميكية
     */
    function initializeDynamicFields() {
        // تحديث حقول التبعية عند تغيير القيم
        $('[data-depends-on]').each(function() {
            const $field = $(this);
            const dependsOn = $field.data('depends-on');
            const $parent = $(`#${dependsOn}`);

            $parent.on('change', function() {
                toggleDependentField($field, $(this));
            });

            // تهيئة الحالة الأولية
            toggleDependentField($field, $parent);
        });
    }

    /**
     * التحقق من صحة الحقول الرقمية
     */
    function validateNumericFields() {
        $('input[type="number"]').each(function() {
            const $input = $(this);
            const val = $input.val();
            const min = $input.attr('min');
            const max = $input.attr('max');

            if (min && val < min) {
                $input.addClass('error');
                showError($input, `القيمة يجب أن تكون ${min} أو أكثر`);
            }
            if (max && val > max) {
                $input.addClass('error');
                showError($input, `القيمة يجب أن تكون ${max} أو أقل`);
            }
        });
    }

    /**
     * إظهار رسالة خطأ للحقل
     */
    function showError($field, message) {
        const $error = $('<div class="field-error"></div>').text(message || 'هذا الحقل مطلوب');
        $field.after($error);
    }

    /**
     * إظهار إشعار
     */
    function showNotification(message, type = 'info') {
        const $notification = $(`
            <div class="settings-notification ${type}">
                <span class="dashicons dashicons-${type === 'error' ? 'warning' : 'info-outline'}"></span>
                ${message}
            </div>
        `);

        $('.auction-settings-container').prepend($notification);
        
        setTimeout(() => {
            $notification.fadeOut(() => $notification.remove());
        }, 5000);
    }

    /**
     * التعامل مع الحقول المعتمدة
     */
    function handleDependencies() {
        // عند تغيير حالة التحميل التدريجي
        $('#lazy_load_enabled').on('change', function() {
            const isEnabled = $(this).is(':checked');
            const $lazyLoadBasicOptions = $('.lazy-load-basic-options');
            const $autoLoadToggle = $('#auto-load-toggle');
            
            if (isEnabled) {
                // إظهار الخيارات الأساسية للتحميل التدريجي
                $lazyLoadBasicOptions.slideDown();
                $autoLoadToggle.slideDown();
            } else {
                // إخفاء كل الخيارات
                $lazyLoadBasicOptions.slideUp();
                $autoLoadToggle.slideUp();
                $('.auto-load-options').slideUp();
                // إعادة تعيين الحالات
                $('#lazy_load_auto_load').prop('checked', false);
            }
        });

        // عند تغيير حالة التحميل التلقائي
        $('#lazy_load_auto_load').on('change', function() {
            const isAutoLoadEnabled = $(this).is(':checked');
            const $autoLoadOptions = $('.auto-load-options');
            
            if (isAutoLoadEnabled) {
                $autoLoadOptions.slideDown();
            } else {
                $autoLoadOptions.slideUp();
            }
        });

        // تشغيل الفحص الأولي عند تحميل الصفحة
        $('#lazy_load_enabled').trigger('change');
        $('#lazy_load_auto_load').trigger('change');
    }

    /**
     * Initialize media uploader for image ads
     */
    function initializeMediaUploader() {
        if (typeof wp === 'undefined' || typeof wp.media === 'undefined') return;

        // $('.upload-image-button').on('click', function(e) { // Old selector
        // $(document).on('click', '.upload-image-button', function(e) { // Old selector for previous ad system
        $(document).on('click', '.upload-image-button-new', function(e) { // Corrected selector for new ad system
            e.preventDefault();
            
            const button = $(this);
            // const imageField = button.siblings('.image-url-field'); // Old way
            const targetFieldId = button.data('target-field');
            const imageField = $('#' + targetFieldId);

            if (!imageField.length) {
                console.error('Target field for image uploader not found:', targetFieldId);
                return;
            }
            
            const frame = wp.media({
                title: 'اختر صورة',
                library: { type: 'image' },
                multiple: false
            });

            frame.on('select', function() {
                const attachment = frame.state().get('selection').first().toJSON();
                imageField.val(attachment.url).trigger('change');
            });

            frame.open();
        });
    }

    /**
     * Initialize toggle switches functionality
     */
    function initializeToggles() {
        $('.setting-toggle input[type="checkbox"]').on('change', function() {
            const toggle = $(this);
            const section = toggle.closest('.setting-toggle');
            const content = section.find('.toggle-content');
            
            if (toggle.is(':checked')) {
                content.slideDown();
            } else {
                content.slideUp();
            }
        });
    }

    // /**
    //  * Initialize ads section functionality (تمت إزالتها لإعادة البناء)
    //  */
    // function initializeAdSettings() {
    //     // ... محتوى الدالة القديم ...
    // }

    /**
     * Initialize new ads settings UI logic
     */
    function initializeNewAdSettingsLogic() {
        // Function to toggle ad content fields based on selected type
        function toggleAdContentFields(selectElement) {
            const $select = $(selectElement);
            const $positionContainer = $select.closest('.ad-position-settings');
            const $htmlGroup = $positionContainer.find('.ad-content-html-group');
            const $imageGroup = $positionContainer.find('.ad-content-image-group');

            if ($select.val() === 'image') {
                $htmlGroup.slideUp();
                $imageGroup.slideDown();
            } else { // html
                $htmlGroup.slideDown();
                $imageGroup.slideUp();
            }
        }

        // Function to toggle all fields within an ad position
        function toggleAdPositionFields(enableToggle) {
            const $toggle = $(enableToggle);
            const $positionContainer = $toggle.closest('.ad-position-settings');
            const $subFields = $positionContainer.find('.form-group'); // Groups for type, html, image

            if ($toggle.is(':checked')) {
                $subFields.slideDown();
                // Also trigger the type select change to show/hide html/image fields correctly
                $positionContainer.find('.ad-type-select-new').trigger('change');
            } else {
                $subFields.slideUp();
            }
        }
        
        // Function to toggle all ad positions based on global ads toggle
        function toggleAllAdPositions(globalEnableToggle) {
            const $globalToggle = $(globalEnableToggle);
            const $allPositionContainers = $('.ads-settings-rebuild .ad-position-settings');
            const $allPositionSeparators = $('.ads-settings-rebuild .ad-position-settings ~ hr'); // Select hr following an ad position

            if ($globalToggle.is(':checked')) {
                // Show the main toggle for each ad position first
                $('.ads-settings-rebuild .setting-toggle-item:not(:first-child)').slideDown(); // Show toggles for each ad position
                $allPositionSeparators.slideDown();

                // Then, for each enabled ad position, show its fields
                $allPositionContainers.each(function() {
                    const $positionToggle = $(this).find('input[type="checkbox"][id$="_enabled"]');
                    if ($positionToggle.is(':checked')) {
                        $(this).find('.form-group').slideDown();
                         // Also trigger the type select change to show/hide html/image fields correctly
                        $(this).find('.ad-type-select-new').trigger('change');
                    } else {
                         $(this).find('.form-group').hide(); // Keep sub-fields hidden if position is not enabled
                    }
                });
            } else {
                $('.ads-settings-rebuild .setting-toggle-item:not(:first-child)').slideUp();
                $allPositionContainers.find('.form-group').slideUp(); // Hide all sub-fields
                $allPositionSeparators.slideUp();
            }
        }


        // Event listener for ad type selection
        $('.ad-type-select-new').on('change', function() {
            toggleAdContentFields(this);
        });

        // Event listener for individual ad position enable/disable toggles
        $('.ad-position-settings input[type="checkbox"][id$="_enabled"]').on('change', function() {
            toggleAdPositionFields(this);
        });
        
        // Event listener for the global ads enable/disable toggle
        $('#new_ads_enabled').on('change', function() {
            toggleAllAdPositions(this);
        });

        // Initial setup on page load
        $('#new_ads_enabled').trigger('change'); // Trigger global toggle first
        // $('.ad-position-settings input[type="checkbox"][id$="_enabled"]').each(function() { // Then individual position toggles
        //     toggleAdPositionFields(this);
        // });
        // $('.ad-type-select-new').each(function() { // Then type selects
        //     toggleAdContentFields(this);
        // });
         // The global trigger should handle the cascading show/hide correctly.
    }

})(jQuery);
