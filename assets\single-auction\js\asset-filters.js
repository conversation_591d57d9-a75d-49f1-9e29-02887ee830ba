/**
 * Asset Filters Handler
 * Handles asset filtering and lazy loading
 */

(function($) {
    'use strict';

    // Cache DOM elements
    const $filtersForm = $('#asset-filters-form');
    const $assetsGrid = $('#auction-assets-grid');
    const $loadingIndicator = $('#loading-indicator');
    const $loadMoreBtn = $('#load-more-assets');
    const $resetBtn = $('#reset-filters');

    // State variables
    let currentPage = 1;
    let isLoading = false;
    let hasMore = true;
    let currentFilters = {};
    let isInitialLoad = true;
    let autoLoadEnabled = false;

    // Initialize
    function init() {
        // Get settings from localized data
        if (typeof auctionData !== 'undefined' && auctionData.lazy_settings) {
            autoLoadEnabled = auctionData.lazy_settings.lazy_load_auto_load === '1';
            
            // Show/hide load more button based on auto load setting
            if ($loadMoreBtn.length) {
                $loadMoreBtn.toggle(!autoLoadEnabled);
            }
        }

        // Load initial assets without filters
        loadAssets(true);
        
        // Bind event handlers
        $filtersForm.on('submit', handleFormSubmit);
        $resetBtn.on('click', handleReset);
        $loadMoreBtn.on('click', handleLoadMore);
        
        // Only bind scroll event if auto load is enabled
        if (autoLoadEnabled) {
            $(window).on('scroll', debounce(handleScroll, 200));
        }
    }

    // Handle form submission
    function handleFormSubmit(e) {
        e.preventDefault();
        currentPage = 1;
        currentFilters = getFormData();
        loadAssets(false);
    }

    // Handle reset button
    function handleReset(e) {
        e.preventDefault();
        $filtersForm[0].reset();
        currentPage = 1;
        currentFilters = {};
        loadAssets(true);
    }

    // Handle load more button
    function handleLoadMore() {
        if (!isLoading && hasMore) {
            currentPage++;
            loadAssets(false);
        }
    }

    // Handle scroll event
    function handleScroll() {
        if (!autoLoadEnabled || isLoading || !hasMore) return;
        
        const scrollDistance = parseInt($assetsGrid.data('scroll-distance')) || 300;
        const scrollPosition = $(window).scrollTop() + $(window).height();
        const loadMorePosition = $assetsGrid.offset().top + $assetsGrid.outerHeight() - scrollDistance;
        
        if (scrollPosition >= loadMorePosition) {
            currentPage++;
            loadAssets(false);
        }
    }

    // Load assets
    function loadAssets(isReset) {
        if (isLoading) return;
        
        showLoading();
        
        const data = {
            action: 'filter_auction_assets',
            nonce: auctionData.nonce,
            auction_id: auctionData.auction_id,
            page: currentPage,
            is_reset: isReset ? 1 : 0,
            ...currentFilters
        };

        $.ajax({
            url: auctionData.ajax_url,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    handleSuccess(response.data);
                } else {
                    handleError(response.data.message || 'Request failed. Please try again.');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', {xhr, status, error});
                handleError('Request failed. Please try again.');
            },
            complete: function() {
                hideLoading();
                isInitialLoad = false;
            }
        });
    }

    // Handle successful response
    function handleSuccess(data) {
        if (currentPage === 1) {
            $assetsGrid.empty();
        }
        
        if (data.html) {
            $assetsGrid.append(data.html);
        }
        
        hasMore = data.has_more;
        updateLoadMoreButton();
        
        // Update total count if available
        if (data.count !== undefined) {
            $('.assets-count-badge .count-number').text(data.count);
        }
    }

    // Handle error
    function handleError(message) {
        if (currentPage === 1) {
            $assetsGrid.html(`
                <div class="error-message" style="text-align: center; padding: 20px;">
                    <p>${message}</p>
                </div>
            `);
        }
    }

    // Get form data
    function getFormData() {
        const formData = {};
        $filtersForm.serializeArray().forEach(item => {
            if (item.value) {
                formData[item.name] = item.value;
            }
        });
        return formData;
    }

    // Update load more button visibility
    function updateLoadMoreButton() {
        if ($loadMoreBtn.length) {
            // Only show load more button if auto load is disabled and there are more items
            $loadMoreBtn.toggle(!autoLoadEnabled && hasMore);
        }
    }

    // Show loading indicator
    function showLoading() {
        isLoading = true;
        
        // If we're loading more (not initial load), show loading state on button
        if (currentPage > 1 && $loadMoreBtn.length) {
            const originalText = $loadMoreBtn.text();
            $loadMoreBtn
                .prop('disabled', true)
                .data('original-text', originalText)
                .html(`
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    جاري التحميل...
                `);
        } else {
            // For initial load or search, show the loading indicator
            $loadingIndicator.show();
        }
    }

    // Hide loading indicator
    function hideLoading() {
        isLoading = false;
        
        // If we were loading more, restore button state
        if (currentPage > 1 && $loadMoreBtn.length) {
            const originalText = $loadMoreBtn.data('original-text');
            $loadMoreBtn
                .prop('disabled', false)
                .text(originalText);
        } else {
            // For initial load or search, hide the loading indicator
            $loadingIndicator.hide();
        }
    }

    // Debounce function
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Initialize when document is ready
    $(document).ready(init);

})(jQuery); 