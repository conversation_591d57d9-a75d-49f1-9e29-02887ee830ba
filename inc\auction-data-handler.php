<?php
/**
 * Auction Data Handler
 *
 * @package AuctionSystem
 * @version 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class AuctionDataHandler {

    /**
     * Get auction details by ID
     */
    public function get_auction_details($auction_id) {
        global $wpdb;

        // Get auction post data
        $post = get_post($auction_id);

        if (!$post) {
            return false;
        }

        if ($post->post_type !== 'auction') {
            return false;
        }

        // Check if auction is published (approved) - also allow 'pending' for testing
        if (!in_array($post->post_status, ['publish', 'pending'])) {
            return false;
        }

        // Get auction meta data
        $auction = array(
            'id' => $post->ID,
            'title' => $post->post_title,
            'description' => $post->post_content,
            'status' => 'approved', // Since it's published
            'created_at' => $post->post_date,
            'updated_at' => $post->post_modified
        );

        // Get meta fields
        $meta_fields = array(
            'auction_date',
            'auction_time',
            'auction_city',
            'auction_location',
            'auction_type',
            'company_name',
            'company_license',
            'company_phone',
            'company_email',
            'company_address',
            'company_website',
            'brochure_url'
        );

        foreach ($meta_fields as $field) {
            // Try both with and without underscore prefix
            $value = get_post_meta($auction_id, $field, true);
            if (empty($value)) {
                $value = get_post_meta($auction_id, '_' . $field, true);
            }
            $auction[$field] = $value;
        }


        // Get pagination settings
        $items_per_page = 2; // Default value, can be overridden by settings
        $current_page = 1;
        
        // Get total count for pagination
        $total_items = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}auction_assets WHERE auction_id = %d",
            $auction_id
        ));
        
        // Calculate pagination
        $offset = ($current_page - 1) * $items_per_page;
        
        // Get paginated assets
        $assets = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}auction_assets 
             WHERE auction_id = %d 
             ORDER BY id ASC
             LIMIT %d OFFSET %d",
            $auction_id,
            $items_per_page,
            $offset
        ), ARRAY_A);

        // Process assets and get images
        $processed_assets = [];

        if (!empty($assets)) {
            foreach ($assets as $asset) {
                // Get asset images (stored as JSON in images field)
                $images_json = $asset['images'];
                $asset['images'] = !empty($images_json) ? json_decode($images_json, true) : [];

                // Add default image if no images exist
                if (empty($asset['images'])) {
                    $default_image_url = get_stylesheet_directory_uri() . '/assets/images/default-property.svg';
                    $asset['images'] = [$default_image_url];
                }

                // Get asset type name from asset types table
                $asset_type = $wpdb->get_var($wpdb->prepare(
                    "SELECT name FROM {$wpdb->prefix}auction_asset_types
                     WHERE id = %d",
                    $asset['asset_type_id']
                ));
                $asset['asset_type'] = $asset_type ?: '';

                // Add location_url field (map_link in database)
                $asset['location_url'] = $asset['map_link'] ?: '';

                // Ensure city field exists for backward compatibility
                if (empty($asset['city']) && !empty($asset['location'])) {
                    // Try to extract city from location field
                    $location_parts = explode(' - ', $asset['location']);
                    $asset['city'] = $location_parts[0] ?? '';
                }

                $processed_assets[] = $asset;
            }
        }

        $auction['assets'] = $processed_assets;

        // Handle auction type - check if it's already a string or needs mapping
        if (!empty($auction['auction_type'])) {
            // If auction_type is already set (string format), use it
            $auction_type_display = $auction['auction_type'];
        } else {
            // Fallback to mapping from auction_type_id if needed
            $auction_types = array(
                'in-person' => 'حضوري',
                'online' => 'إلكتروني',
                'hybrid' => 'مختلط'
            );
            $auction_type_display = isset($auction_types[$auction['auction_type']])
                ? $auction_types[$auction['auction_type']]
                : 'حضوري';
        }

        // Map English values to Arabic
        $type_mapping = array(
            'in-person' => 'حضوري',
            'online' => 'إلكتروني',
            'hybrid' => 'مختلط'
        );

        $auction['auction_type_display'] = isset($type_mapping[$auction['auction_type']])
            ? $type_mapping[$auction['auction_type']]
            : $auction['auction_type'];

        // Set brochure field
        $auction['brochure'] = $auction['brochure_url'];

        // Add city field for backward compatibility and easier access
        $auction['city'] = $auction['auction_city'];

        // Handle auction location URL
        if (!empty($auction['auction_location'])) {
            // Use the saved Google Maps URL if available
            $auction['auction_location_url'] = $auction['auction_location'];
        } elseif (!empty($auction['auction_city'])) {
            // Create a Google Maps search URL for the city as fallback
            $auction['auction_location_url'] = 'https://www.google.com/maps/search/' . urlencode($auction['auction_city'] . ', Saudi Arabia');
        } else {
            $auction['auction_location_url'] = '';
        }

        // Add view count
        $auction['view_count'] = $this->get_auction_views($auction_id);

        // Add formatted dates
        $auction['formatted_date'] = $this->format_auction_date($auction['auction_date'], $auction['auction_time']);

        // Add asset count
        $auction['asset_count'] = count($processed_assets);

        return $auction;
    }

    /**
     * Get advertisement content by location
     */
    public function get_ad_content($location) {
        global $wpdb;
        
        // Start output buffering for debug info
        ob_start();
        echo "<!-- DEBUG Ad Info - Location: {$location} -->\n";
        
        // Check if the ads are configured properly
        // Try both new format (with new_ads_ prefix) and old format
        $new_prefix = "new_ads_{$location}";
        $old_prefix = $location;
        
        echo "<!-- Trying with new prefix: {$new_prefix} and old prefix: {$old_prefix} -->\n";
        
        // First check if global ads are enabled (try both formats)
        $new_global = $wpdb->get_var("SELECT setting_value FROM {$wpdb->prefix}auction_settings WHERE setting_name = 'new_ads_enabled'");
        $old_global = $wpdb->get_var("SELECT setting_value FROM {$wpdb->prefix}auction_settings WHERE setting_name = 'ads_enabled'");
        
        $ads_globally_enabled = !empty($new_global) ? $new_global : $old_global;
        
        echo "<!-- Global ads setting (new): {$new_global} -->\n";
        echo "<!-- Global ads setting (old): {$old_global} -->\n";
        echo "<!-- Global ads enabled (used): {$ads_globally_enabled} -->\n";
        
        // DEBUG: Force ads enabled for testing
        if (empty($ads_globally_enabled)) {
            echo "<!-- FORCED ENABLED: No global setting found, forcing enabled for debug -->\n";
            $ads_globally_enabled = '1';
        }
        
        // Exit if global ads are disabled
        if ($ads_globally_enabled !== '1') {
            echo "<!-- Ads globally disabled -->\n";
            $debug_output = ob_get_clean();
            return $debug_output . '<!-- Ads globally disabled -->';
        }
        
        // Check if this specific ad position is enabled (try both formats)
        $new_position_enabled = $wpdb->get_var("SELECT setting_value FROM {$wpdb->prefix}auction_settings WHERE setting_name = '{$new_prefix}_enabled'");
        $old_position_enabled = $wpdb->get_var("SELECT setting_value FROM {$wpdb->prefix}auction_settings WHERE setting_name = '{$old_prefix}_enabled'");
        
        $ad_position_enabled = !empty($new_position_enabled) ? $new_position_enabled : $old_position_enabled;
        
        echo "<!-- Position enabled (new): {$new_position_enabled} -->\n";
        echo "<!-- Position enabled (old): {$old_position_enabled} -->\n";
        echo "<!-- Position enabled (used): {$ad_position_enabled} -->\n";
        
        // DEBUG: Force position enabled for testing
        if (empty($ad_position_enabled)) {
            echo "<!-- FORCED ENABLED: No position setting found, forcing enabled for debug -->\n";
            $ad_position_enabled = '1';
        }
        
        // Exit if this position is disabled
        if ($ad_position_enabled !== '1') {
            echo "<!-- Ad position {$location} is disabled -->\n";
            $debug_output = ob_get_clean();
            return $debug_output . '<!-- Ad position disabled -->';
        }
        
        // Get the ad type (try both formats)
        $new_ad_type = $wpdb->get_var("SELECT setting_value FROM {$wpdb->prefix}auction_settings WHERE setting_name = '{$new_prefix}_type'");
        $old_ad_type = $wpdb->get_var("SELECT setting_value FROM {$wpdb->prefix}auction_settings WHERE setting_name = '{$old_prefix}_type'");
        
        $ad_type = !empty($new_ad_type) ? $new_ad_type : $old_ad_type;
        
        echo "<!-- Ad type (new): {$new_ad_type} -->\n";
        echo "<!-- Ad type (old): {$old_ad_type} -->\n";
        echo "<!-- Ad type (used): {$ad_type} -->\n";
        
        // Default to HTML if no type is specified
        if (empty($ad_type)) {
            echo "<!-- No ad type found, defaulting to 'html' -->\n";
            $ad_type = 'html';
        }
        
        // Get the ad content based on type (try all possible keys)
        $ad_main_content = '';
        $potential_content_keys = array();
        
        // Try both new and old prefixes with all possible content key formats
        if ($ad_type === 'image') {
            $potential_content_keys = array(
                $new_prefix . '_content',
                $new_prefix . '_image_url',
                $new_prefix . '_image',
                $old_prefix . '_content',
                $old_prefix . '_image_url',
                $old_prefix . '_image'
            );
        } else { // html or any other type
            $potential_content_keys = array(
                $new_prefix . '_content_html',
                $new_prefix . '_content',
                $new_prefix . '_html',
                $old_prefix . '_content_html',
                $old_prefix . '_content',
                $old_prefix . '_html'
            );
        }
        
        echo "<!-- Trying content keys: " . implode(', ', $potential_content_keys) . " -->\n";
        
        // Try each key until we find content
        $found_key = '';
        foreach ($potential_content_keys as $content_key) {
            echo "<!-- Checking key: {$content_key} -->\n";
            
            $content = $wpdb->get_var("SELECT setting_value FROM {$wpdb->prefix}auction_settings WHERE setting_name = '{$content_key}'");
            
            if (!empty($content)) {
                echo "<!-- Found content with key: {$content_key} -->\n";
                $ad_main_content = $content;
                $found_key = $content_key;
                break;
            }
        }
        
        echo "<!-- Content found: " . (!empty($ad_main_content) ? 'YES' : 'NO') . " with key: {$found_key} -->\n";
        
        // If no content found, create a test ad (for debugging only)
        if (empty($ad_main_content)) {
            echo "<!-- No content found in database, using test content -->\n";
            
            // Create visible test content for debugging
            $ad_main_content = '<div style="background:#f0ad4e; color:#fff; text-align:center; padding:10px; margin:10px 0; border:1px solid #eea236;">';
            $ad_main_content .= '<strong>' . ucfirst(str_replace('_', ' ', $location)) . ' Test Ad</strong> - This is a placeholder for debugging.';
            $ad_main_content .= '</div>';
        }
        
        // Process the content based on ad type
        if ($ad_type === 'image') {
            // Get additional image settings
            // Try both new prefix and old prefix for image link URL
            $image_link_url = $wpdb->get_var("SELECT setting_value FROM {$wpdb->prefix}auction_settings WHERE setting_name = '{$new_prefix}_image_link_url'");
            
            // If not found with new prefix, try old prefix
            if (empty($image_link_url)) {
                $image_link_url = $wpdb->get_var("SELECT setting_value FROM {$wpdb->prefix}auction_settings WHERE setting_name = '{$old_prefix}_image_link_url'");
            }
            // Try both new prefix and old prefix for image alt text
            $image_alt_text = $wpdb->get_var("SELECT setting_value FROM {$wpdb->prefix}auction_settings WHERE setting_name = '{$new_prefix}_image_alt'");
            
            // If not found with new prefix, try old prefix
            if (empty($image_alt_text)) {
                $image_alt_text = $wpdb->get_var("SELECT setting_value FROM {$wpdb->prefix}auction_settings WHERE setting_name = '{$old_prefix}_image_alt'");
            }
            
            // Default alt text if none found
            if (empty($image_alt_text)) {
                $image_alt_text = ucfirst(str_replace('_', ' ', $location)) . ' Advertisement';
            }

            echo "<!-- Image link URL: " . htmlspecialchars($image_link_url) . " -->\n";
            echo "<!-- Image alt text: " . htmlspecialchars($image_alt_text) . " -->\n";

            $link_href = !empty($image_link_url) ? esc_url($image_link_url) : '#';
            $link_target = !empty($image_link_url) && $image_link_url !== '#' ? ' target="_blank" rel="noopener"' : '';
            $alt_attr = !empty($image_alt_text) ? esc_attr($image_alt_text) : __('Advertisement', 'auction-system');

            $html = sprintf(
                '<a href="%s"%s class="ad-image-link"><img src="%s" alt="%s" class="ad-image" style="max-width: 100%%; height: auto;"></a>',
                $link_href,
                $link_target,
                esc_url($ad_main_content),
                $alt_attr
            );
            
            
            // Get debug output and append to HTML
            $debug_output = ob_get_clean();
            return $debug_output . $html;
        } else {
            // For HTML or Google Ads
            $html = wp_kses_post($ad_main_content);
            
            // Get debug output and append to HTML
            $debug_output = ob_get_clean();
            return $debug_output . $html;
        }
    }

    /**
     * Check if auction is expired
     */
    public function is_auction_expired($auction_date, $auction_time) {
        $auction_datetime = strtotime($auction_date . ' ' . $auction_time);
        return time() > $auction_datetime;
    }

    /**
     * Get time remaining for auction
     */
    public function get_time_remaining($auction_date, $auction_time) {
        $auction_datetime = strtotime($auction_date . ' ' . $auction_time);
        $current_time = time();

        if ($current_time >= $auction_datetime) {
            return [
                'expired' => true,
                'days' => 0,
                'hours' => 0,
                'minutes' => 0,
                'seconds' => 0
            ];
        }

        $diff = $auction_datetime - $current_time;

        return [
            'expired' => false,
            'days' => floor($diff / (60 * 60 * 24)),
            'hours' => floor(($diff % (60 * 60 * 24)) / (60 * 60)),
            'minutes' => floor(($diff % (60 * 60)) / 60),
            'seconds' => $diff % 60,
            'total_seconds' => $diff
        ];
    }

    /**
     * Format auction date for display
     */
    public function format_auction_date($date, $time) {
        if (empty($date)) return '';

        $datetime = $date . ' ' . $time;
        $timestamp = strtotime($datetime);

        if (!$timestamp) return $date;

        return [
            'date' => date_i18n('j F Y', $timestamp),
            'time' => date_i18n('g:i A', $timestamp),
            'day' => date_i18n('l', $timestamp),
            'datetime' => $datetime,
            'timestamp' => $timestamp
        ];
    }

    /**
     * Get related auctions
     */
    public function get_related_auctions($current_auction_id, $limit = 3) {
        $args = array(
            'post_type' => 'auction',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'post__not_in' => array($current_auction_id),
            'orderby' => 'date',
            'order' => 'DESC'
        );

        $query = new WP_Query($args);
        $related = array();

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $post_id = get_the_ID();

                $related[] = array(
                    'id' => $post_id,
                    'title' => get_the_title(),
                    'auction_date' => get_post_meta($post_id, 'auction_date', true),
                    'auction_time' => get_post_meta($post_id, 'auction_time', true),
                    'city' => get_post_meta($post_id, 'auction_city', true)
                );
            }
            wp_reset_postdata();
        }

        return $related;
    }

    /**
     * Log auction view
     */
    public function log_auction_view($auction_id) {
        // For now, we'll use post meta to track views
        // This is simpler and doesn't require additional tables
        $view_count = get_post_meta($auction_id, 'auction_views', true);
        $view_count = $view_count ? intval($view_count) : 0;

        // Check if user already viewed today (using session/cookie)
        $viewed_today = isset($_SESSION['viewed_auctions']) &&
                       in_array($auction_id, $_SESSION['viewed_auctions']);

        if (!$viewed_today) {
            // Increment view count
            update_post_meta($auction_id, 'auction_views', $view_count + 1);

            // Mark as viewed in session
            if (!isset($_SESSION['viewed_auctions'])) {
                $_SESSION['viewed_auctions'] = array();
            }
            $_SESSION['viewed_auctions'][] = $auction_id;
        }
    }

    /**
     * Get auction view count
     */
    public function get_auction_views($auction_id) {
        $view_count = get_post_meta($auction_id, 'auction_views', true);
        return $view_count ? intval($view_count) : 0;
    }
}
