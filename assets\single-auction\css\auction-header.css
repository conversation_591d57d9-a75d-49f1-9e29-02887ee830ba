/**
 * Single Auction Page - Header Styles
 *
 * @package AuctionSystem
 * @version 1.0.0
 * @description أنماط رأس الصفحة وشارات الحالة
 */

/* Auction Header */
.auction-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    border-radius: 12px;
    margin-bottom: 40px;
    position: relative;
    overflow: hidden;
}

.auction-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    z-index: 1;
}

.auction-header > * {
    position: relative;
    z-index: 2;
}

.auction-status-badge {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-upcoming {
    background: #27ae60;
    color: white;
}

.status-ongoing {
    background: #f39c12;
    color: white;
}

.status-ended {
    background: #e74c3c;
    color: white;
}

.auction-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 20px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.auction-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 20px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
    font-weight: 500;
}

.meta-item svg {
    opacity: 0.8;
}

/* Top Meta Section */
.auction-meta-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.auction-breadcrumb {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.auction-breadcrumb a {
    color: white;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.auction-breadcrumb a:hover {
    opacity: 0.8;
}

.auction-breadcrumb .separator {
    opacity: 0.6;
}

.auction-breadcrumb .current {
    font-weight: 600;
}

.auction-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Title Section */
.auction-title-section {
    margin-bottom: 30px;
}

.auction-title {
    line-height: 1.2;
}

.auction-meta .meta-item {
    background: rgba(255, 255, 255, 0.15);
    padding: 10px 15px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
    font-size: 0.95rem;
}

.auction-meta .meta-item svg {
    opacity: 0.9;
    flex-shrink: 0;
}

.type-badge {
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    padding: 6px 12px;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.85rem;
}

/* Location Link in Header */
.location-link-header {
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid transparent;
    padding-bottom: 2px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.location-link-header:hover {
    color: rgba(255, 255, 255, 0.8);
    border-bottom-color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    transform: translateY(-1px);
}

.location-link-header:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
    border-radius: 2px;
}

/* Countdown Timer */
.auction-countdown {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
}

.countdown-container h3 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
}

.countdown {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.countdown-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.2);
    padding: 15px 10px;
    border-radius: 10px;
    min-width: 70px;
    backdrop-filter: blur(5px);
}

.countdown-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 5px;
}

.countdown-label {
    font-size: 0.85rem;
    opacity: 0.9;
    font-weight: 500;
}

/* Expired Countdown */
.countdown-expired {
    text-align: center;
    padding: 30px;
    background: rgba(231, 76, 60, 0.2);
    border-radius: 15px;
    backdrop-filter: blur(5px);
}

.expired-icon {
    font-size: 3rem;
    display: block;
    margin-bottom: 15px;
}

.expired-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: #e74c3c;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .auction-header {
        padding: 25px 20px;
        margin-bottom: 30px;
    }

    .auction-meta-top {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .auction-title {
        font-size: 1.8rem;
    }

    .auction-meta {
        gap: 10px;
    }

    .auction-meta .meta-item {
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    .countdown {
        gap: 10px;
    }

    .countdown-item {
        min-width: 60px;
        padding: 12px 8px;
    }

    .countdown-number {
        font-size: 1.5rem;
    }

    .countdown-label {
        font-size: 0.8rem;
    }
}
