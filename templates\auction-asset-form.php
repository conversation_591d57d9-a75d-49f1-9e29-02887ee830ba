<?php
/**
 * Asset Form Template
 *
 * @package AuctionSystem
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get asset types
global $wpdb;
$asset_types_table = $wpdb->prefix . 'auction_asset_types';
$asset_types = $wpdb->get_results("SELECT * FROM $asset_types_table WHERE is_active = 1 ORDER BY sort_order ASC");

// Get settings
$auction_settings = AuctionSettings::get_instance();
$enable_starting_price = $auction_settings->get_setting('enable_starting_price', '0') === '1';
$enable_minimum_bid = $auction_settings->get_setting('enable_minimum_bid', '0') === '1';
$max_images = $auction_settings->get_setting('max_images_per_asset', '10');

$asset_index = isset($asset_index) ? $asset_index : 0;
$asset_data = isset($asset_data) ? $asset_data : array();
?>

<div class="asset-item" data-asset-index="<?php echo $asset_index; ?>">
    <div class="asset-header">
        <h4 class="asset-title">
            <?php printf(__('الأصل رقم %d', 'auction-system'), $asset_index + 1); ?>
        </h4>
        <div class="asset-actions">
            <button type="button" class="btn btn-small btn-secondary edit-asset" data-asset="<?php echo $asset_index; ?>">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
                <?php _e('تحرير', 'auction-system'); ?>
            </button>
            <button type="button" class="btn btn-small btn-danger remove-asset" data-asset="<?php echo $asset_index; ?>">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="3,6 5,6 21,6"></polyline>
                    <path d="M19,6V20A2,2 0 0,1 17,20H7A2,2 0 0,1 5,20V6M8,6V4A2,2 0 0,1 10,2H14A2,2 0 0,1 16,4V6"></path>
                </svg>
                <?php _e('حذف', 'auction-system'); ?>
            </button>
        </div>
    </div>

    <div class="asset-content" style="display: none;">
        <div class="form-row">
            <div class="form-group col-md-6">
                <label for="asset_title_<?php echo $asset_index; ?>" class="required">
                    <?php _e('عنوان الأصل', 'auction-system'); ?> *
                </label>
                <input type="text"
                       id="asset_title_<?php echo $asset_index; ?>"
                       name="assets[<?php echo $asset_index; ?>][title]"
                       class="form-control"
                       required
                       value="<?php echo esc_attr($asset_data['title'] ?? ''); ?>"
                       placeholder="<?php _e('مثال: أرض سكنية في حي النرجس', 'auction-system'); ?>">
                <div class="error-message"></div>
            </div>

            <div class="form-group col-md-6">
                <label for="asset_type_<?php echo $asset_index; ?>" class="required">
                    <?php _e('نوع الأصل', 'auction-system'); ?> *
                </label>
                <select id="asset_type_<?php echo $asset_index; ?>"
                        name="assets[<?php echo $asset_index; ?>][asset_type_id]"
                        class="form-control"
                        required>
                    <option value=""><?php _e('اختر نوع الأصل', 'auction-system'); ?></option>
                    <?php foreach ($asset_types as $type): ?>
                        <option value="<?php echo $type->id; ?>"
                                <?php selected($asset_data['asset_type_id'] ?? '', $type->id); ?>>
                            <?php echo esc_html($type->name); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <div class="error-message"></div>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="asset_description_<?php echo $asset_index; ?>">
                    <?php _e('وصف الأصل', 'auction-system'); ?>
                </label>
                <textarea id="asset_description_<?php echo $asset_index; ?>"
                          name="assets[<?php echo $asset_index; ?>][description]"
                          class="form-control"
                          rows="3"
                          placeholder="<?php _e('وصف تفصيلي للأصل...', 'auction-system'); ?>"><?php echo esc_textarea($asset_data['description'] ?? ''); ?></textarea>
                <div class="error-message"></div>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group col-md-4">
                <label for="asset_area_<?php echo $asset_index; ?>" class="required">
                    <?php _e('المساحة', 'auction-system'); ?> *
                </label>
                <input type="text"
                       id="asset_area_<?php echo $asset_index; ?>"
                       name="assets[<?php echo $asset_index; ?>][area]"
                       class="form-control"
                       required
                       value="<?php echo esc_attr($asset_data['area'] ?? ''); ?>"
                       placeholder="<?php _e('مثال: 500 متر مربع', 'auction-system'); ?>">
                <div class="error-message"></div>
            </div>

            <div class="form-group col-md-4">
                <label for="asset_city_<?php echo $asset_index; ?>" class="required">
                    <?php _e('المدينة', 'auction-system'); ?> *
                </label>
                <input type="text"
                       id="asset_city_<?php echo $asset_index; ?>"
                       name="assets[<?php echo $asset_index; ?>][city]"
                       class="form-control"
                       required
                       value="<?php echo esc_attr($asset_data['city'] ?? ''); ?>"
                       placeholder="<?php _e('الرياض', 'auction-system'); ?>">
                <div class="error-message"></div>
            </div>

            <div class="form-group col-md-4">
                <label for="asset_district_<?php echo $asset_index; ?>">
                    <?php _e('الحي', 'auction-system'); ?>
                </label>
                <input type="text"
                       id="asset_district_<?php echo $asset_index; ?>"
                       name="assets[<?php echo $asset_index; ?>][district]"
                       class="form-control"
                       value="<?php echo esc_attr($asset_data['district'] ?? ''); ?>"
                       placeholder="<?php _e('النرجس', 'auction-system'); ?>">
                <div class="error-message"></div>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="asset_deed_<?php echo $asset_index; ?>">
                    <?php _e('رقم الصك888', 'auction-system'); ?>
                </label>
                <input type="text"
                       id="asset_deed_<?php echo $asset_index; ?>"
                       name="assets[<?php echo $asset_index; ?>][deed_number]"
                       class="form-control"
                       value="<?php echo esc_attr($asset_data['deed_number'] ?? ''); ?>"
                       placeholder="<?php _e('رقم صك الملكية', 'auction-system'); ?>">
                <div class="error-message"></div>
            </div>
        </div>

        <?php if ($enable_starting_price || $enable_minimum_bid): ?>
        <div class="form-row">
            <?php if ($enable_starting_price): ?>
            <div class="form-group col-md-6">
                <label for="asset_starting_price_<?php echo $asset_index; ?>">
                    <?php _e('السعر المبدئي (ريال)', 'auction-system'); ?>
                </label>
                <input type="number"
                       id="asset_starting_price_<?php echo $asset_index; ?>"
                       name="assets[<?php echo $asset_index; ?>][starting_price]"
                       class="form-control"
                       min="0"
                       step="0.01"
                       value="<?php echo esc_attr($asset_data['starting_price'] ?? ''); ?>"
                       placeholder="<?php _e('0.00', 'auction-system'); ?>">
                <div class="error-message"></div>
            </div>
            <?php endif; ?>

            <?php if ($enable_minimum_bid): ?>
            <div class="form-group col-md-6">
                <label for="asset_minimum_bid_<?php echo $asset_index; ?>">
                    <?php _e('الحد الأدنى للمزايدة (ريال)', 'auction-system'); ?>
                </label>
                <input type="number"
                       id="asset_minimum_bid_<?php echo $asset_index; ?>"
                       name="assets[<?php echo $asset_index; ?>][minimum_bid]"
                       class="form-control"
                       min="0"
                       step="0.01"
                       value="<?php echo esc_attr($asset_data['minimum_bid'] ?? ''); ?>"
                       placeholder="<?php _e('0.00', 'auction-system'); ?>">
                <div class="error-message"></div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <div class="form-row">
            <div class="form-group">
                <label for="asset_map_link_<?php echo $asset_index; ?>">
                    <?php _e('رابط خرائط جوجل', 'auction-system'); ?>
                </label>
                <input type="url"
                       id="asset_map_link_<?php echo $asset_index; ?>"
                       name="assets[<?php echo $asset_index; ?>][map_link]"
                       class="form-control map-link-input"
                       value="<?php echo esc_attr($asset_data['map_link'] ?? ''); ?>"
                       placeholder="https://www.google.com/maps/place/...">
                <div class="error-message"></div>
                <p class="description">
                    <?php _e('انسخ رابط الموقع من خرائط جوجل لعرض الموقع على الخريطة', 'auction-system'); ?>
                </p>
                <div class="map-container" style="display: none;"></div>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="asset_images_<?php echo $asset_index; ?>">
                    <?php _e('صور الأصل', 'auction-system'); ?>
                </label>
                <div class="file-upload-area" data-asset-index="<?php echo $asset_index; ?>">
                    <div class="upload-icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <circle cx="8.5" cy="8.5" r="1.5"></circle>
                            <polyline points="21,15 16,10 5,21"></polyline>
                        </svg>
                    </div>
                    <div class="upload-text"><?php _e('اضغط لاختيار الصور أو اسحبها هنا', 'auction-system'); ?></div>
                    <div class="upload-hint">
                        <?php
                        printf(__('يمكن رفع حتى %d صور، الحد الأقصى %s ميجابايت لكل صورة', 'auction-system'),
                               $max_images,
                               $auction_settings->get_setting('max_image_size_mb', '5'));
                        ?>
                    </div>
                </div>
                <input type="file"
                       id="asset_images_<?php echo $asset_index; ?>"
                       name="asset_images_temp_<?php echo $asset_index; ?>[]"
                       class="image-upload-input"
                       data-asset-index="<?php echo $asset_index; ?>"
                       accept="image/*"
                       multiple
                       style="display: none;">

                <div class="image-preview" data-asset-index="<?php echo $asset_index; ?>">
                    <!-- Uploaded images will appear here -->
                </div>

                <!-- Hidden inputs to store final image data -->
                <div class="image-data-container" data-asset-index="<?php echo $asset_index; ?>">
                    <!-- Hidden inputs for image data will be added here -->
                </div>
            </div>
        </div>

        <div class="asset-actions-bottom" style="text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #e1e8ed;">
            <button type="button" class="btn btn-secondary collapse-asset" data-asset="<?php echo $asset_index; ?>">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="18,15 12,9 6,15"></polyline>
                </svg>
                <?php _e('طي الأصل', 'auction-system'); ?>
            </button>
        </div>
    </div>

    <div class="asset-summary" style="display: block;">
        <div class="summary-grid">
            <div class="summary-item">
                <strong><?php _e('العنوان:', 'auction-system'); ?></strong>
                <span class="summary-title"><?php echo esc_html($asset_data['title'] ?? __('غير محدد', 'auction-system')); ?></span>
            </div>
            <div class="summary-item">
                <strong><?php _e('النوع:', 'auction-system'); ?></strong>
                <span class="summary-type"><?php echo esc_html($asset_data['type_name'] ?? __('غير محدد', 'auction-system')); ?></span>
            </div>
            <div class="summary-item">
                <strong><?php _e('المساحة:', 'auction-system'); ?></strong>
                <span class="summary-area"><?php echo esc_html($asset_data['area'] ?? __('غير محدد', 'auction-system')); ?></span>
            </div>
            <div class="summary-item">
                <strong><?php _e('الموقع:', 'auction-system'); ?></strong>
                <span class="summary-location"><?php echo esc_html(($asset_data['city'] ?? '') . ' - ' . ($asset_data['district'] ?? '')); ?></span>
            </div>
        </div>
    </div>
</div>
