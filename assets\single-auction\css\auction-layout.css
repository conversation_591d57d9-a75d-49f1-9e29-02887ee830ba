/**
 * Single Auction Page - Layout Styles
 *
 * @package AuctionSystem
 * @version 1.0.0
 * @description تخطيط الصفحة والشبكات والتنسيق العام
 */

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 40px;
    align-items: start;
}
/* Auction Info Grid - Applied to the container of description/map and sidebar */
.auction-info-grid {
    display: grid;
    grid-template-columns: 1fr 350px; /* Main content column and sidebar column */
    gap: 40px; /* Gap between columns */
    align-items: start; /* Align items to the start of the grid area */
}

.main-content {
    min-width: 0;
}

.sidebar-content {
    position: sticky;
    top: 20px;
}

/* Auction Description */
.auction-description {
    background: #fff;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.auction-description h3 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

.description-content {
    color: #34495e;
    line-height: 1.8;
    font-size: 1.1rem;
}

.description-content p {
    margin-bottom: 15px;
}

/* Assets Section */
.auction-assets {
    background: #fff;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.auction-assets h3 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 30px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

.assets-grid {
    display: flex;
    flex-direction: column;
    gap: 30px;
}
