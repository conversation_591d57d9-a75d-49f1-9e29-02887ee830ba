<?php
/**
 * قسم إعدادات التحميل التدريجي
 *
 * @package AuctionSystem
 * @subpackage Settings
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<!-- Lazy Loading Settings Section -->
<div class="settings-section">
    <div class="section-header">
        <h2>
            <span class="dashicons dashicons-update"></span>
            <?php _e('إعدادات التحميل التدريجي', 'auction-system'); ?>
        </h2>
        <p class="section-description">
            <?php _e('تحسين أداء الصفحة من خلال التحميل التدريجي للأصول', 'auction-system'); ?>
        </p>
    </div>

    <div class="settings-toggles">
        <!-- Enable Lazy Loading -->
        <div class="setting-toggle">
            <div class="toggle-content">
                <div class="toggle-info">
                    <h3>
                        <span class="dashicons dashicons-update"></span>
                        <?php _e('تفعيل التحميل التدريجي', 'auction-system'); ?>
                    </h3>
                    <p><?php _e('تحميل الأصول تدريجياً لتحسين سرعة الصفحة', 'auction-system'); ?></p>
                </div>
                <div class="toggle-switch">
                    <label class="switch">
                        <input type="checkbox" id="lazy_load_enabled" name="lazy_load_enabled"
                               value="1" <?php checked($settings['lazy_load_enabled'], '1'); ?> />
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Auto Load - يظهر فقط إذا كان التحميل التدريجي مفعل -->
        <div class="setting-toggle" id="auto-load-toggle" <?php echo $settings['lazy_load_enabled'] !== '1' ? 'style="display:none;"' : ''; ?>>
            <div class="toggle-content">
                <div class="toggle-info">
                    <h3>
                        <span class="dashicons dashicons-controls-forward"></span>
                        <?php _e('التحميل التلقائي', 'auction-system'); ?>
                    </h3>
                    <p><?php _e('تحميل المزيد من الأصول تلقائياً عند التمرير', 'auction-system'); ?></p>
                </div>
                <div class="toggle-switch">
                    <label class="switch">
                        <input type="checkbox" id="lazy_load_auto_load" name="lazy_load_auto_load"
                               value="1" <?php checked($settings['lazy_load_auto_load'], '1'); ?> />
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="settings-grid">
        <!-- إعدادات التحميل التدريجي -->
        <div class="lazy-load-basic-options" <?php echo $settings['lazy_load_enabled'] !== '1' ? 'style="display:none;"' : ''; ?>>
            <!-- Items Per Page -->
            <div class="setting-item">
                <label for="lazy_load_per_page" class="setting-label">
                    <span class="dashicons dashicons-grid-view"></span>
                    <?php _e('عدد الأصول المعروضة أولاً', 'auction-system'); ?>
                </label>
                <div class="setting-input-group">
                    <input type="number" id="lazy_load_per_page" name="lazy_load_per_page"
                           value="<?php echo esc_attr($settings['lazy_load_per_page']); ?>"
                           min="1" max="20" class="setting-input" />
                    <span class="input-suffix"><?php _e('أصل', 'auction-system'); ?></span>
                </div>
                <p class="setting-description"><?php _e('عدد الأصول التي تظهر في البداية قبل التحميل التدريجي', 'auction-system'); ?></p>
            </div>

            <!-- Animation Type -->
            <div class="setting-item">
                <label for="lazy_load_animation" class="setting-label">
                    <span class="dashicons dashicons-art"></span>
                    <?php _e('نوع التأثير المرئي', 'auction-system'); ?>
                </label>
                <select id="lazy_load_animation" name="lazy_load_animation" class="setting-input">
                    <option value="fade" <?php selected($settings['lazy_load_animation'], 'fade'); ?>><?php _e('تلاشي', 'auction-system'); ?></option>
                    <option value="slide" <?php selected($settings['lazy_load_animation'], 'slide'); ?>><?php _e('انزلاق', 'auction-system'); ?></option>
                    <option value="scale" <?php selected($settings['lazy_load_animation'], 'scale'); ?>><?php _e('تكبير', 'auction-system'); ?></option>
                    <option value="none" <?php selected($settings['lazy_load_animation'], 'none'); ?>><?php _e('بدون تأثير', 'auction-system'); ?></option>
                </select>
                <p class="setting-description"><?php _e('التأثير المرئي عند ظهور الأصول الجديدة', 'auction-system'); ?></p>
            </div>
        </div>

        <!-- إعدادات التحميل التلقائي -->
        <div class="auto-load-options" <?php echo ($settings['lazy_load_enabled'] !== '1' || $settings['lazy_load_auto_load'] !== '1') ? 'style="display:none;"' : ''; ?>>
            <!-- Scroll Distance -->
            <div class="setting-item">
                <label for="lazy_load_scroll_distance" class="setting-label">
                    <span class="dashicons dashicons-arrow-down-alt"></span>
                    <?php _e('مسافة التحميل التلقائي', 'auction-system'); ?>
                </label>
                <div class="setting-input-group">
                    <input type="number" id="lazy_load_scroll_distance" name="lazy_load_scroll_distance"
                           value="<?php echo esc_attr($settings['lazy_load_scroll_distance']); ?>"
                           min="100" max="1000" step="50" class="setting-input" />
                    <span class="input-suffix"><?php _e('بكسل', 'auction-system'); ?></span>
                </div>
                <p class="setting-description"><?php _e('المسافة من أسفل الصفحة لبدء التحميل التلقائي للمزيد من الأصول', 'auction-system'); ?></p>
            </div>
        </div>
    </div>
</div>
