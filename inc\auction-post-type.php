<?php
/**
 * Auction Post Type Registration
 *
 * @package AuctionSystem
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Auction Post Type Class
 */
class AuctionPostType {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }

    /**
     * Initialize
     */
    private function init() {
        add_action('init', array($this, 'register_post_type'));
        add_action('init', array($this, 'register_taxonomies'));
        add_filter('post_updated_messages', array($this, 'updated_messages'));

        // Flush rewrite rules on activation
        register_activation_hook(__FILE__, array($this, 'flush_rewrite_rules'));
        add_action('after_switch_theme', array($this, 'flush_rewrite_rules'));
    }

    /**
     * Register auction post type
     */
    public function register_post_type() {
        $labels = array(
            'name'                  => __('المزادات', 'auction-system'),
            'singular_name'         => __('مزاد', 'auction-system'),
            'menu_name'             => __('المزادات', 'auction-system'),
            'name_admin_bar'        => __('مزاد', 'auction-system'),
            'archives'              => __('أرشيف المزادات', 'auction-system'),
            'attributes'            => __('خصائص المزاد', 'auction-system'),
            'parent_item_colon'     => __('المزاد الأب:', 'auction-system'),
            'all_items'             => __('جميع المزادات', 'auction-system'),
            'add_new_item'          => __('إضافة مزاد جديد', 'auction-system'),
            'add_new'               => __('إضافة جديد', 'auction-system'),
            'new_item'              => __('مزاد جديد', 'auction-system'),
            'edit_item'             => __('تحرير المزاد', 'auction-system'),
            'update_item'           => __('تحديث المزاد', 'auction-system'),
            'view_item'             => __('عرض المزاد', 'auction-system'),
            'view_items'            => __('عرض المزادات', 'auction-system'),
            'search_items'          => __('البحث في المزادات', 'auction-system'),
            'not_found'             => __('لم يتم العثور على مزادات', 'auction-system'),
            'not_found_in_trash'    => __('لم يتم العثور على مزادات في المهملات', 'auction-system'),
            'featured_image'        => __('صورة المزاد المميزة', 'auction-system'),
            'set_featured_image'    => __('تعيين صورة مميزة', 'auction-system'),
            'remove_featured_image' => __('إزالة الصورة المميزة', 'auction-system'),
            'use_featured_image'    => __('استخدام كصورة مميزة', 'auction-system'),
            'insert_into_item'      => __('إدراج في المزاد', 'auction-system'),
            'uploaded_to_this_item' => __('تم رفعه لهذا المزاد', 'auction-system'),
            'items_list'            => __('قائمة المزادات', 'auction-system'),
            'items_list_navigation' => __('التنقل في قائمة المزادات', 'auction-system'),
            'filter_items_list'     => __('تصفية قائمة المزادات', 'auction-system'),
        );

        $args = array(
            'label'                 => __('مزاد', 'auction-system'),
            'description'           => __('نظام إدارة المزادات', 'auction-system'),
            'labels'                => $labels,
            'supports'              => array('title', 'editor', 'thumbnail', 'author'),
            'taxonomies'            => array('auction_type'),
            'hierarchical'          => false,
            'public'                => true,
            'show_ui'               => true,
            'show_in_menu'          => false,
            'menu_position'         => 30,
            'menu_icon'             => 'dashicons-awards',
            'show_in_admin_bar'     => true,
            'show_in_nav_menus'     => true,
            'can_export'            => true,
            'has_archive'           => false,
            'exclude_from_search'   => false,
            'publicly_queryable'    => true,
            'capability_type'       => 'post',
            'show_in_rest'          => true,
            'rewrite'               => array(
                'slug'                  => 'auction',
                'with_front'            => false,
                'pages'                 => true,
                'feeds'                 => true,
            ),
        );

        register_post_type('auction', $args);
    }

    /**
     * Register taxonomies
     */
    public function register_taxonomies() {
        // Register auction type taxonomy
        $labels = array(
            'name'                       => __('أنواع المزادات', 'auction-system'),
            'singular_name'              => __('نوع المزاد', 'auction-system'),
            'menu_name'                  => __('أنواع المزادات', 'auction-system'),
            'all_items'                  => __('جميع الأنواع', 'auction-system'),
            'parent_item'                => __('النوع الأب', 'auction-system'),
            'parent_item_colon'          => __('النوع الأب:', 'auction-system'),
            'new_item_name'              => __('اسم النوع الجديد', 'auction-system'),
            'add_new_item'               => __('إضافة نوع جديد', 'auction-system'),
            'edit_item'                  => __('تحرير النوع', 'auction-system'),
            'update_item'                => __('تحديث النوع', 'auction-system'),
            'view_item'                  => __('عرض النوع', 'auction-system'),
            'separate_items_with_commas' => __('فصل الأنواع بفواصل', 'auction-system'),
            'add_or_remove_items'        => __('إضافة أو إزالة أنواع', 'auction-system'),
            'choose_from_most_used'      => __('اختر من الأكثر استخداماً', 'auction-system'),
            'popular_items'              => __('الأنواع الشائعة', 'auction-system'),
            'search_items'               => __('البحث في الأنواع', 'auction-system'),
            'not_found'                  => __('لم يتم العثور على أنواع', 'auction-system'),
            'no_terms'                   => __('لا توجد أنواع', 'auction-system'),
            'items_list'                 => __('قائمة الأنواع', 'auction-system'),
            'items_list_navigation'      => __('التنقل في قائمة الأنواع', 'auction-system'),
        );

        $args = array(
            'labels'                     => $labels,
            'hierarchical'               => false,
            'public'                     => true,
            'show_ui'                    => true,
            'show_admin_column'          => true,
            'show_in_nav_menus'          => true,
            'show_tagcloud'              => false,
            'show_in_rest'               => true,
            'rewrite'                    => array(
                'slug'                       => 'auction-type',
                'with_front'                 => false,
                'hierarchical'               => false,
            ),
        );

        register_taxonomy('auction_type', array('auction'), $args);

        // Insert default auction types
        $this->insert_default_auction_types();
    }

    /**
     * Insert default auction types
     */
    private function insert_default_auction_types() {
        $default_types = array(
            array(
                'name' => __('حضوري', 'auction-system'),
                'slug' => 'in-person'
            ),
            array(
                'name' => __('إلكتروني', 'auction-system'),
                'slug' => 'online'
            ),
            array(
                'name' => __('هجين', 'auction-system'),
                'slug' => 'hybrid'
            )
        );

        foreach ($default_types as $type) {
            if (!term_exists($type['slug'], 'auction_type')) {
                wp_insert_term($type['name'], 'auction_type', array('slug' => $type['slug']));
            }
        }
    }

    /**
     * Custom post updated messages
     */
    public function updated_messages($messages) {
        $post             = get_post();
        $post_type        = get_post_type($post);
        $post_type_object = get_post_type_object($post_type);

        if ('auction' !== $post_type) {
            return $messages;
        }

        $messages['auction'] = array(
            0  => '', // Unused. Messages start at index 1.
            1  => __('تم تحديث المزاد.', 'auction-system'),
            2  => __('تم تحديث الحقل المخصص.', 'auction-system'),
            3  => __('تم حذف الحقل المخصص.', 'auction-system'),
            4  => __('تم تحديث المزاد.', 'auction-system'),
            5  => isset($_GET['revision']) ? sprintf(__('تم استرداد المزاد من المراجعة من %s', 'auction-system'), wp_post_revision_title((int) $_GET['revision'], false)) : false,
            6  => __('تم نشر المزاد.', 'auction-system'),
            7  => __('تم حفظ المزاد.', 'auction-system'),
            8  => __('تم إرسال المزاد للمراجعة.', 'auction-system'),
            9  => sprintf(
                __('تم جدولة المزاد لـ: <strong>%1$s</strong>.', 'auction-system'),
                date_i18n(__('M j, Y @ G:i'), strtotime($post->post_date))
            ),
            10 => __('تم تحديث مسودة المزاد.', 'auction-system')
        );

        return $messages;
    }

    /**
     * Flush rewrite rules
     */
    public function flush_rewrite_rules() {
        flush_rewrite_rules();
    }
}

// Initialize the auction post type
AuctionPostType::get_instance();
