<?php
/**
 * Auction Edit Handler
 *
 * @package AuctionSystem
 * @version 1.0.0
 * @description معالج تعديل المزادات - يتعامل مع تحميل وحفظ بيانات المزاد للتعديل
 */

if (!defined('ABSPATH')) {
    exit;
}

class AuctionEditHandler {

    private static $instance = null;

    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action('init', array($this, 'init'));
    }

    public function init() {
        // Hook to override default edit page for auctions
        add_action('admin_init', array($this, 'redirect_auction_edit'));

        // Handle edit form submission
        add_action('wp_ajax_save_auction_edit', array($this, 'handle_edit_submission'));
        add_action('wp_ajax_nopriv_save_auction_edit', array($this, 'handle_edit_submission'));

        // No need to create page here - handled in functions.php
    }

    /**
     * Redirect auction edit to custom edit page
     */
    public function redirect_auction_edit() {
        global $pagenow;

        if ($pagenow === 'post.php' && isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['post'])) {
            $post_id = intval($_GET['post']);
            $post = get_post($post_id);

            if ($post && $post->post_type === 'auction') {
                // Redirect to custom edit page
                $edit_url = home_url('/edit-auction/?auction_id=' . $post_id);
                wp_redirect($edit_url);
                exit;
            }
        }
    }

    /**
     * Get auction data for editing
     */
    public function get_auction_data($auction_id) {
        $auction_id = intval($auction_id);

        // Get auction post
        $auction_post = get_post($auction_id);
        if (!$auction_post || $auction_post->post_type !== 'auction') {
            return false;
        }

        // Get auction meta data
        $auction_data = array(
            'id' => $auction_id,
            'title' => $auction_post->post_title,
            'description' => $auction_post->post_content,
            'status' => $auction_post->post_status,
            'date' => get_post_meta($auction_id, '_auction_date', true),
            'time' => get_post_meta($auction_id, '_auction_time', true),
            'city' => get_post_meta($auction_id, '_auction_city', true),
            'location' => get_post_meta($auction_id, '_auction_location', true),
            'type' => get_post_meta($auction_id, '_auction_type', true),
            'company_name' => get_post_meta($auction_id, '_company_name', true),
            'company_license' => get_post_meta($auction_id, '_company_license', true),
            'company_phone' => get_post_meta($auction_id, '_company_phone', true),
            'company_email' => get_post_meta($auction_id, '_company_email', true),
            'company_address' => get_post_meta($auction_id, '_company_address', true),
            'company_website' => get_post_meta($auction_id, '_company_website', true),
            'brochure_url' => get_post_meta($auction_id, '_brochure_url', true)
        );

        // Get auction assets
        $auction_data['assets'] = $this->get_auction_assets($auction_id);

        return $auction_data;
    }

    /**
     * Get auction assets
     */
    private function get_auction_assets($auction_id) {
        global $wpdb;

        $assets_table = $wpdb->prefix . 'auction_assets';

        $assets = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$assets_table}
             WHERE auction_id = %d
             ORDER BY sort_order ASC",
            $auction_id
        ), ARRAY_A);

        $processed_assets = array();

        foreach ($assets as $asset) {
            // Decode images JSON
            $asset['images'] = !empty($asset['images']) ? json_decode($asset['images'], true) : array();

            // Get asset type name
            $asset_type = $wpdb->get_var($wpdb->prepare(
                "SELECT name FROM {$wpdb->prefix}auction_asset_types WHERE id = %d",
                $asset['asset_type_id']
            ));
            $asset['asset_type_name'] = $asset_type ?: '';

            $processed_assets[] = $asset;
        }

        return $processed_assets;
    }

    /**
     * Handle edit form submission
     */
    public function handle_edit_submission() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['auction_nonce'], 'auction_edit_nonce')) {
            wp_die('Security check failed');
        }

        // Check permissions
        if (!current_user_can('edit_posts')) {
            wp_die('Insufficient permissions');
        }

        $auction_id = intval($_POST['auction_id']);

        // Update auction post
        $this->update_auction_post($auction_id, $_POST);

        // Update auction meta
        $this->update_auction_meta($auction_id, $_POST);

        // Update auction assets
        if (isset($_POST['assets']) && is_array($_POST['assets'])) {
            $this->update_auction_assets($auction_id, $_POST['assets'], $_FILES);
        }

        // Handle brochure update/removal
        if (isset($_POST['remove_brochure']) && $_POST['remove_brochure'] === '1') {
            // Remove existing brochure
            $this->remove_brochure($auction_id);
        } elseif (isset($_POST['brochure_data']) && !empty($_POST['brochure_data'])) {
            // Update with new brochure
            $this->handle_brochure_update($auction_id, $_POST);
        }

        // Redirect back to edit page with success message
        $redirect_url = home_url('/edit-auction/?auction_id=' . $auction_id . '&updated=1');
        wp_redirect($redirect_url);
        exit;
    }

    /**
     * Update auction post
     */
    private function update_auction_post($auction_id, $data) {
        $post_data = array(
            'ID' => $auction_id,
            'post_title' => sanitize_text_field($data['auction_title']),
            'post_content' => sanitize_textarea_field($data['auction_description'])
        );

        wp_update_post($post_data);
    }

    /**
     * Update auction meta data
     */
    private function update_auction_meta($auction_id, $data) {
        $meta_fields = array(
            'auction_date', 'auction_time', 'auction_city', 'auction_location', 'auction_type',
            'company_name', 'company_license', 'company_phone', 'company_email',
            'company_address', 'company_website'
        );

        foreach ($meta_fields as $field) {
            if (isset($data[$field])) {
                $value = sanitize_text_field($data[$field]);
                update_post_meta($auction_id, '_' . $field, $value);
            }
        }
    }

    /**
     * Update auction assets
     */
    private function update_auction_assets($auction_id, $assets, $files = array()) {
        global $wpdb;

        $assets_table = $wpdb->prefix . 'auction_assets';

        // Delete existing assets
        $wpdb->delete($assets_table, array('auction_id' => $auction_id));

        // Re-insert updated assets
        foreach ($assets as $index => $asset) {
            // Handle asset images
            $asset_images = array();

            // Check for uploaded files for this asset
            $file_key = "asset_images_{$index}";
            if (isset($files[$file_key]) && is_array($files[$file_key]['name'])) {
                $uploaded_files = $files[$file_key];

                for ($i = 0; $i < count($uploaded_files['name']); $i++) {
                    if ($uploaded_files['error'][$i] === UPLOAD_ERR_OK) {
                        $file_data = array(
                            'name' => $uploaded_files['name'][$i],
                            'type' => $uploaded_files['type'][$i],
                            'tmp_name' => $uploaded_files['tmp_name'][$i],
                            'error' => $uploaded_files['error'][$i],
                            'size' => $uploaded_files['size'][$i]
                        );

                        // Use existing upload function
                        if (class_exists('AuctionFrontend')) {
                            $frontend = AuctionFrontend::get_instance();
                            $upload_result = $frontend->upload_and_optimize_image($file_data);
                            if ($upload_result['success']) {
                                $asset_images[] = $upload_result['data']['url'];
                            }
                        }
                    }
                }
            }

            // If no new uploaded files, keep existing images
            if (empty($asset_images) && isset($asset['existing_images']) && is_array($asset['existing_images'])) {
                $asset_images = $asset['existing_images'];
            }

            $asset_data = array(
                'auction_id' => $auction_id,
                'asset_type_id' => intval($asset['asset_type_id']),
                'title' => sanitize_text_field($asset['title']),
                'description' => sanitize_textarea_field($asset['description']),
                'area' => sanitize_text_field($asset['area']),
                'location' => sanitize_text_field($asset['location']),
                'city' => sanitize_text_field($asset['city']),
                'district' => sanitize_text_field($asset['district']),
                'map_link' => esc_url_raw($asset['map_link']),
                'coordinates' => sanitize_text_field($asset['coordinates']),
                'deed_number' => sanitize_text_field($asset['deed_number']),
                'images' => !empty($asset_images) ? json_encode($asset_images) : '',
                'sort_order' => $index + 1
            );

            // Add price fields if enabled
            if (class_exists('AuctionSettings')) {
                $auction_settings = AuctionSettings::get_instance();
                if ($auction_settings->get_setting('enable_starting_price', '0') === '1') {
                    $asset_data['starting_price'] = !empty($asset['starting_price']) ? floatval($asset['starting_price']) : null;
                }
                if ($auction_settings->get_setting('enable_minimum_bid', '0') === '1') {
                    $asset_data['minimum_bid'] = !empty($asset['minimum_bid']) ? floatval($asset['minimum_bid']) : null;
                }
            }

            $wpdb->insert($assets_table, $asset_data);
        }
    }

    /**
     * Handle brochure update
     */
    private function handle_brochure_update($auction_id, $data) {
        if (class_exists('AuctionFrontend')) {
            $frontend = AuctionFrontend::get_instance();
            $frontend->handle_brochure_upload_base64($auction_id, $data);
        }
    }

    /**
     * Remove brochure
     */
    private function remove_brochure($auction_id) {
        // Get current brochure URL
        $brochure_url = get_post_meta($auction_id, '_brochure_url', true);

        if (!empty($brochure_url)) {
            // Delete the file if it exists
            $upload_dir = wp_upload_dir();
            $file_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $brochure_url);

            if (file_exists($file_path)) {
                wp_delete_file($file_path);
            }

            // Remove from database
            delete_post_meta($auction_id, '_brochure_url');
        }
    }

    /**
     * Create edit auction page
     */
    public function create_edit_auction_page() {
        // Only run once per session to avoid multiple calls
        static $page_created = false;
        if ($page_created) {
            return;
        }
        $page_created = true;

        // Check if page already exists
        $page = get_page_by_path('edit-auction');

        if (!$page) {
            // Create the page
            $page_data = array(
                'post_title' => 'تعديل المزاد',
                'post_content' => '[edit_auction_form]',
                'post_status' => 'publish',
                'post_type' => 'page',
                'post_name' => 'edit-auction'
            );

            $page_id = wp_insert_post($page_data);

            if ($page_id && !is_wp_error($page_id)) {
                // Set page template
                update_post_meta($page_id, '_wp_page_template', 'page-edit-auction.php');

                // Flush rewrite rules to make sure the page is accessible
                flush_rewrite_rules();
            }
        }
    }

    /**
     * Get asset types for dropdown
     */
    public function get_asset_types() {
        global $wpdb;

        $asset_types = $wpdb->get_results(
            "SELECT id, name FROM {$wpdb->prefix}auction_asset_types ORDER BY name ASC",
            ARRAY_A
        );

        return $asset_types;
    }
}

// Initialize the edit handler
AuctionEditHandler::get_instance();
