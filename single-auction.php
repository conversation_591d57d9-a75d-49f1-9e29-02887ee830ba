<?php
/**
 * Single Auction Page Template
 *
 * @package AuctionSystem
 * @version 1.0.0
 */

// Start session before any output
if (!session_id()) {
    session_start();
}

get_header();

// Get auction data
$auction_id = get_the_ID();

// Include required files
require_once get_stylesheet_directory() . '/inc/auction-data-handler.php';

// Initialize data handler
$auction_data = new AuctionDataHandler();

$auction_details = $auction_data->get_auction_details($auction_id);

if ($auction_details) {
    $auction_data->log_auction_view($auction_id);
}

// Debug: Log auction details for troubleshooting
if (isset($_GET['debug']) && current_user_can('manage_options')) {
    echo '<pre style="background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc; direction: ltr; text-align: left;">';
    echo "Auction ID: " . $auction_id . "\n";
    echo "Auction Details:\n";
    print_r($auction_details);
    echo '</pre>';
}

// Check if auction exists and is approved
if (!$auction_details) {
    ?>
    <div class="auction-not-found">
        <div class="container">
            <div class="not-found-content">
                <h1><?php _e('المزاد غير موجود', 'auction-system'); ?></h1>
                <p><?php _e('عذراً، المزاد المطلوب غير موجود أو لم يتم الموافقة عليه بعد.', 'auction-system'); ?></p>
                <a href="<?php echo home_url(); ?>" class="btn btn-primary">
                    <?php _e('العودة للرئيسية', 'auction-system'); ?>
                </a>
            </div>
        </div>
    </div>
    <?php
    get_footer();
    return;
}

// Enqueue enhanced CSS files for single auction page
wp_enqueue_style('auction-enhanced-css', get_stylesheet_directory_uri() . '/assets/single-auction/css/auction-enhanced.css', array(), AUCTION_VERSION);
wp_enqueue_style('auction-notifications-css', get_stylesheet_directory_uri() . '/assets/single-auction/css/auction-notifications.css', array('auction-enhanced-css'), AUCTION_VERSION);

// The main auction script is now loaded via the auction_enqueue_scripts function in functions.php
// This ensures all scripts are loaded in the correct order and dependencies are managed properly

// Get lazy loading settings from auction settings
require_once get_stylesheet_directory() . '/inc/auction-settings.php';
$auction_settings = AuctionSettings::get_instance();

$lazy_settings = array(
    'lazy_load_enabled' => $auction_settings->get_setting('lazy_load_enabled', '1'),
    'lazy_load_per_page' => $auction_settings->get_setting('lazy_load_per_page', '3'),
    'lazy_load_auto_load' => $auction_settings->get_setting('lazy_load_auto_load', '1'),
    'lazy_load_scroll_distance' => $auction_settings->get_setting('lazy_load_scroll_distance', '300'),
    'lazy_load_animation' => $auction_settings->get_setting('lazy_load_animation', 'fade')
);

// (تمت إزالة تمرير بيانات الأصول والإعدادات للـ JS هنا - يتم تمريرها فقط من auction_display_assets)

// Include component files
require_once get_stylesheet_directory() . '/inc/auction-components/ad-spaces.php';
require_once get_stylesheet_directory() . '/inc/auction-components/auction-header.php';
require_once get_stylesheet_directory() . '/inc/auction-components/auction-description.php';
// require_once get_stylesheet_directory() . '/inc/auction-components/auction-location.php'; // Auction location component removed
require_once get_stylesheet_directory() . '/inc/auction-components/auction-assets.php';
require_once get_stylesheet_directory() . '/inc/auction-components/auction-sidebar.php';
?>

<div class="single-auction-page">
    <!-- Scroll Progress Bar -->
    <div class="scroll-progress"></div>
    <?php auction_display_top_banner_ad($auction_data); ?>

    <div class="container">
        <?php auction_display_header($auction_details, $auction_id); ?>

        <!-- Auction Info Grid -->
        <div class="auction-info-grid">
            <div class="auction-info-section">
                <?php auction_display_description($auction_details); ?>

                <?php
                // Display Embedded Google Map based on auction_location_url
                $location_url = isset($auction_details['auction_location_url']) ? trim($auction_details['auction_location_url']) : '';
                $map_embed_html = '';

                if (!empty($location_url)) {
                    $embed_data = null;
                    $latitude = null;
                    $longitude = null;
                    $place_name = null;

                    // Try to extract coordinates like @24.452068,39.6662948
                    if (preg_match('/@([-+]?\d{1,2}\.\d+),([-+]?\d{1,3}\.\d+)/', $location_url, $coord_matches)) {
                        $latitude = $coord_matches[1];
                        $longitude = $coord_matches[2];
                    }
                    // Try to extract place name like /place/Place+Name/
                    elseif (preg_match('/\/place\/([^\/@]+)/', $location_url, $place_matches)) {
                        $place_name = urldecode($place_matches[1]);
                    }
                    // Try to extract from daddr= or query=
                    elseif (preg_match('/[?&](?:daddr|q)=([^&]+)/', $location_url, $query_matches)) {
                        // Check if the query is coordinates
                        if (preg_match('/^([-+]?\d{1,2}\.\d+),([-+]?\d{1,3}\.\d+)$/', urldecode($query_matches[1]), $q_coord_matches)) {
                            $latitude = $q_coord_matches[1];
                            $longitude = $q_coord_matches[2];
                        } else {
                            $place_name = urldecode($query_matches[1]);
                        }
                    }


                    $google_embed_url_base = 'https://maps.google.com/maps?hl=ar&z=15&output=embed&q=';

                    if ($latitude && $longitude) {
                        $embed_data = $latitude . ',' . $longitude;
                    } elseif ($place_name) {
                        $embed_data = urlencode($place_name);
                    }

                    if ($embed_data) {
                        $final_embed_url = $google_embed_url_base . $embed_data;
                        $map_embed_html = '
                        <div class="auction-embedded-map-container" style="margin-top: 30px; padding-top: 25px; border-top: 1px solid #e0e0e0;">
                            <h4 style="font-size: 1.3rem; color: #2c3e50; margin-bottom: 20px; padding-bottom: 10px; border-bottom: 2px solid #3498db; display: inline-block;">
                                <i class="fas fa-map-marked-alt" style="margin-right: 8px; color: #3498db;"></i>' . __('موقع المزاد على الخريطة', 'auction-system') . '
                            </h4>
                            <div id="map-placeholder-' . esc_attr($auction_id) . '" style="text-align:center; padding: 50px 0; border: 1px dashed #ccc; border-radius: 8px; background-color: #f9f9f9;">
                                <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: #3498db; margin-bottom:10px;"></i>
                                <p style="font-size: 1rem; color: #555;">' . __('جاري تحميل الخريطة...', 'auction-system') . '</p>
                            </div>
                            <div id="map-iframe-container-' . esc_attr($auction_id) . '" class="google-map-iframe-wrapper" style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden; border-radius: 8px; border: 1px solid #ccc; display: none;">
                                <iframe
                                    id="auction-google-map-iframe-' . esc_attr($auction_id) . '"
                                    src="' . esc_url($final_embed_url) . '"
                                    style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border:0;"
                                    allowfullscreen=""
                                    loading="lazy"
                                    referrerpolicy="no-referrer-when-downgrade"
                                    onload="document.getElementById(\'map-iframe-container-' . esc_attr($auction_id) . '\').style.display=\'block\'; document.getElementById(\'map-placeholder-' . esc_attr($auction_id) . '\').style.display=\'none\';"
                                ></iframe>
                            </div>
                            <script>
                                // Fallback to show map if onload doesn\'t fire quickly
                                setTimeout(function() {
                                    var iframeContainer = document.getElementById(\'map-iframe-container-' . esc_attr($auction_id) . '\');
                                    var placeholder = document.getElementById(\'map-placeholder-' . esc_attr($auction_id) . '\');
                                    if (iframeContainer && placeholder && window.getComputedStyle(iframeContainer).display === \'none\') {
                                        // Check if iframe has content (simple check, might not be foolproof)
                                        var iframe = document.getElementById(\'auction-google-map-iframe-' . esc_attr($auction_id) . '\');
                                        if (iframe && iframe.src) { // Check if src is set, indicating it might load
                                           iframeContainer.style.display = \'block\';
                                           placeholder.style.display = \'none\';
                                        }
                                    }
                                }, 3000); // Try to show after 3 seconds
                            </script>
                             <p style="margin-top: 15px; text-align:center;">
                                <a href="' . esc_url($location_url) . '" target="_blank" rel="noopener noreferrer" class="btn btn-secondary" style="display: inline-flex; align-items: center; text-decoration: none;">
                                    <i class="fas fa-external-link-alt" style="margin-left: 8px;"></i>' . __('فتح في Google Maps', 'auction-system') . '
                                </a>
                            </p>
                        </div>';
                    }
                }

                // Fallback to direct link if iframe couldn't be constructed but URL exists
                if (empty($map_embed_html) && !empty($location_url)) {
                    $map_embed_html = '
                    <div class="auction-location-link-container" style="margin-top: 30px; padding-top: 25px; border-top: 1px solid #e0e0e0;">
                         <h4 style="font-size: 1.3rem; color: #2c3e50; margin-bottom: 20px; padding-bottom: 10px; border-bottom: 2px solid #3498db; display: inline-block;">
                            <i class="fas fa-map-marker-alt" style="margin-right: 8px; color: #c0392b;"></i>' . __('موقع المزاد', 'auction-system') . '
                        </h4>
                        <p><a href="' . esc_url($location_url) . '" target="_blank" rel="noopener noreferrer" class="btn btn-primary" style="background: #c0392b; color: white;">
                            <i class="fas fa-external-link-alt" style="margin-left: 8px;"></i>' . __('عرض موقع المزاد على جوجل ماب', 'auction-system') . '
                        </a></p>
                    </div>';
                }
                
                // Optional: Message if no location URL is set and no embed could be made
                // if (empty($map_embed_html)) {
                //     $map_embed_html = '<p style="margin-top: 20px; color: #7f8c8d;">' . __('لم يتم تحديد موقع لهذا المزاد.', 'auction-system') . '</p>';
                // }

                echo $map_embed_html;
                ?>
                <?php
                // Auction location map display removed
                // if (function_exists('auction_display_location_map')) {
                //     auction_display_location_map($auction_details);
                // }
                ?>
            </div>
            <?php auction_display_sidebar($auction_details, $auction_data); ?>
        </div>
    </div>
    <?php 
        if (function_exists('auction_display_content_middle_ad')) {
            auction_display_content_middle_ad($auction_data);
        } 
    ?>
    <!-- Full Width Assets Section -->
    <div class="container">
        <?php auction_display_assets($auction_details); ?>
    </div>

    <?php auction_display_bottom_banner_ad($auction_data); ?>
</div>

<?php
// Enqueue elegant auction styles
wp_enqueue_style(
    'elegant-layout-css',
    get_stylesheet_directory_uri() . '/assets/single-auction/css/elegant-layout.css',
    array(),
    AUCTION_VERSION
);

wp_enqueue_style(
    'elegant-assets-css',
    get_stylesheet_directory_uri() . '/assets/single-auction/css/elegant-assets.css',
    array('elegant-layout-css'),
    AUCTION_VERSION
);

wp_enqueue_style(
    'elegant-gallery-css',
    get_stylesheet_directory_uri() . '/assets/single-auction/css/elegant-gallery.css',
    array('elegant-assets-css'),
    AUCTION_VERSION
);

wp_enqueue_style(
    'elegant-asset-details-css',
    get_stylesheet_directory_uri() . '/assets/single-auction/css/elegant-asset-details.css',
    array('elegant-assets-css'),
    AUCTION_VERSION
);

// Enqueue elegant JavaScript ONCE in the footer (after all assets are rendered)
add_action('wp_footer', function() {
    wp_enqueue_script(
        'elegant-assets-interactions-js',
        get_stylesheet_directory_uri() . '/assets/single-auction/js/elegant-assets-interactions.js',
        array('jquery'),
        AUCTION_VERSION,
        true
    );
}, 100);

get_footer();
?>
