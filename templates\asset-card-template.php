<?php
/**
 * Unified Asset Card Template
 * 
 * @package AuctionSystem
 * @version 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get unified asset card HTML
 * 
 * @param array $asset Asset data
 * @param int $index Asset index
 * @return string HTML output
 */
function get_unified_asset_card($asset, $index = 0) {
    static $displayed_assets = [];
    
    // Log if we've seen this asset before
    if (in_array($asset['id'], $displayed_assets)) {
        error_log('WARNING: Duplicate asset detected! Asset ID: ' . $asset['id']);
    } else {
        $displayed_assets[] = $asset['id'];
    }
    
    ob_start();
    ?>
    <div class="elegant-asset-card" data-asset-id="<?php echo esc_attr($asset['id']); ?>" data-asset-index="<?php echo $index; ?>">
        <!-- Asset Image Section (Right Side) -->
        <div class="asset-image-section">
            <?php 
            // Check if gallery function exists
            if (function_exists('auction_display_horizontal_gallery')) {
                auction_display_horizontal_gallery($asset);
            } else {
                // Fallback to basic image display
                if (!empty($asset['images']) && is_array($asset['images'])) {
                    $first_image = reset($asset['images']);
                    if (!empty($first_image['url'])) {
                        echo '<img src="' . esc_url($first_image['url']) . '" alt="' . esc_attr($asset['title']) . '" class="asset-image">';
                    }
                }
            }
            ?>
        </div>

        <!-- Asset Content Section (Left Side) -->
        <div class="asset-content-section">
            <!-- Card Header -->
            <div class="elegant-card-header">
                <div class="asset-title-badge">
                    <h3 class="asset-title-text"><?php echo esc_html($asset['title']); ?></h3>
                </div>
                <?php if (!empty($asset['asset_type'])): ?>
                    <div class="asset-type-elegant">
                        <span><?php echo esc_html($asset['asset_type']); ?></span>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Asset Details Container -->
            <div class="asset-details-container">
                <?php 
                // Use the asset details function if it exists, otherwise display basic info
                if (function_exists('auction_display_asset_details')) {
                    auction_display_asset_details($asset);
                } else {
                    echo '<div class="asset-description">' . wp_kses_post(wp_trim_words($asset['description'], 30, '...')) . '</div>';
                }
                ?>
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
} 