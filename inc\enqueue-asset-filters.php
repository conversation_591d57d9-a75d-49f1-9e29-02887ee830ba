<?php
/**
 * Enqueue styles and scripts for Asset Filters.
 *
 * @package AuctionSystem
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

if (!function_exists('auction_system_child_enqueue_asset_filters_assets')) {
    /**
     * Enqueues styles and scripts for the asset filters on single auction pages.
     */
    function auction_system_child_enqueue_asset_filters_assets() {
        // Ensure this runs only on single 'auction' post type pages.
        // Replace 'auction' with your actual auction post type slug if different.
        if (is_singular('auction')) {

            // Enqueue Filter CSS
            wp_enqueue_style(
                'auction-asset-filters-css', // Handle
                get_stylesheet_directory_uri() . '/assets/single-auction/css/asset-filters.css', // Path to CSS file
                array(), // Dependencies (e.g., main theme stylesheet handle)
                '1.0.0'  // Version
            );
            
            // Enqueue noUiSlider CSS
            wp_enqueue_style(
                'nouislider-css', // handle for the CSS file
                get_stylesheet_directory_uri() . '/assets/libs/nouislider/nouislider.min.css', // adjust the path to match where you saved the file
                array(), // any other CSS dependencies
                '15.7.1' // replace this with the version of noUiSlider you downloaded
            );

            // Enqueue noUiSlider JS
            wp_enqueue_script(
                'nouislider-js', // اسم معرّف (handle) لملف JS
                get_stylesheet_directory_uri() . '/assets/libs/nouislider/nouislider.min.js', // اضبط المسار
                array(), // هل يعتمد على jQuery؟ (عادةً لا، ولكن تحقق من وثائق noUiSlider)
                '15.7.1', // استبدل هذا برقم إصدار noUiSlider
                true // تحميل في الفوتر (موصى به)
            );


            // Enqueue Filter JavaScript
            wp_enqueue_script(
                'auction-asset-filters-js',
                get_stylesheet_directory_uri() . '/assets/single-auction/js/range-asset-filters.js',
                array('jquery', 'nouislider-js'), 
                '1.0.1', 
                true
            );

            // Localize script with data for AJAX and other needs
            wp_localize_script(
                'auction-asset-filters-js', // Script handle to attach data to
                'assetFiltersData',         // JavaScript object name to access data (e.g., assetFiltersData.ajax_url)
                array(                    'ajax_url'           => admin_url('admin-ajax.php'),
                    'nonce'              => wp_create_nonce('asset_filters_nonce'), // Security nonce
                    'auction_id'         => get_the_ID(), // Get current auction ID
                    'loading_message'    => esc_html__('جاري تحميل الأصول...', 'auction-system'),
                    'no_results_message' => esc_html__('لا توجد أصول تطابق معايير البحث.', 'auction-system')
                    // Add any other data/strings your JS might need
                )
            );
        }
    }
}
add_action('wp_enqueue_scripts', 'auction_system_child_enqueue_asset_filters_assets');