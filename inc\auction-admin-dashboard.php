<?php
/**
 * Auction Admin Dashboard
 *
 * @package AuctionSystem
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Auction Admin Dashboard Class
 */
class AuctionAdminDashboard {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_ajax_approve_auction', array($this, 'approve_auction'));
        add_action('wp_ajax_reject_auction', array($this, 'reject_auction'));
        add_action('wp_ajax_unapprove_auction', array($this, 'unapprove_auction'));
        add_action('wp_ajax_reopen_auction', array($this, 'reopen_auction'));
        add_action('wp_ajax_delete_auction', array($this, 'delete_auction'));
    }

    /**
     * Render dashboard
     */
    public function render() {
        // Handle bulk actions
        if (isset($_POST['action']) && $_POST['action'] !== '-1') {
            $this->handle_bulk_actions();
        }

        // Get auctions data
        $auctions_data = $this->get_auctions_data();
        $stats = $this->get_stats();

        ?>
        <div class="wrap auction-admin-dashboard">
            <h1><?php _e('إدارة المزادات', 'auction-system'); ?></h1>

            <!-- Statistics Cards -->
            <div class="auction-stats-cards">
                <div class="stats-card">
                    <div class="stats-icon">
                        <span class="dashicons dashicons-awards"></span>
                    </div>
                    <div class="stats-content">
                        <h3><?php echo number_format_i18n($stats['total']); ?></h3>
                        <p><?php _e('إجمالي المزادات', 'auction-system'); ?></p>
                    </div>
                </div>

                <div class="stats-card pending">
                    <div class="stats-icon">
                        <span class="dashicons dashicons-clock"></span>
                    </div>
                    <div class="stats-content">
                        <h3><?php echo number_format_i18n($stats['pending']); ?></h3>
                        <p><?php _e('في انتظار المراجعة', 'auction-system'); ?></p>
                    </div>
                </div>

                <div class="stats-card approved">
                    <div class="stats-icon">
                        <span class="dashicons dashicons-yes-alt"></span>
                    </div>
                    <div class="stats-content">
                        <h3><?php echo number_format_i18n($stats['approved']); ?></h3>
                        <p><?php _e('معتمد', 'auction-system'); ?></p>
                    </div>
                </div>

                <div class="stats-card rejected">
                    <div class="stats-icon">
                        <span class="dashicons dashicons-dismiss"></span>
                    </div>
                    <div class="stats-content">
                        <h3><?php echo number_format_i18n($stats['rejected']); ?></h3>
                        <p><?php _e('مرفوض', 'auction-system'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="auction-filters">
                <form method="get">
                    <input type="hidden" name="page" value="auction-system">

                    <select name="status" onchange="this.form.submit()">
                        <option value=""><?php _e('جميع الحالات', 'auction-system'); ?></option>
                        <option value="pending" <?php selected($_GET['status'] ?? '', 'pending'); ?>><?php _e('في انتظار المراجعة', 'auction-system'); ?></option>
                        <option value="publish" <?php selected($_GET['status'] ?? '', 'publish'); ?>><?php _e('معتمد', 'auction-system'); ?></option>
                        <option value="draft" <?php selected($_GET['status'] ?? '', 'draft'); ?>><?php _e('مسودة', 'auction-system'); ?></option>
                    </select>

                    <input type="text" name="search" value="<?php echo esc_attr($_GET['search'] ?? ''); ?>" placeholder="<?php _e('البحث في المزادات...', 'auction-system'); ?>">
                    <input type="submit" class="button" value="<?php _e('بحث', 'auction-system'); ?>">
                </form>
            </div>

            <!-- Auctions Table -->
            <form method="post">
                <?php wp_nonce_field('auction_bulk_action', 'auction_bulk_nonce'); ?>

                <div class="tablenav top">
                    <div class="alignleft actions bulkactions">
                        <select name="action">
                            <option value="-1"><?php _e('إجراءات مجمعة', 'auction-system'); ?></option>
                            <option value="approve"><?php _e('اعتماد', 'auction-system'); ?></option>
                            <option value="unapprove"><?php _e('إلغاء الاعتماد', 'auction-system'); ?></option>
                            <option value="reject"><?php _e('رفض', 'auction-system'); ?></option>
                            <option value="reopen"><?php _e('إعادة للمراجعة', 'auction-system'); ?></option>
                            <option value="delete"><?php _e('حذف', 'auction-system'); ?></option>
                        </select>
                        <input type="submit" class="button action" value="<?php _e('تطبيق', 'auction-system'); ?>">
                    </div>
                </div>

                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <td class="manage-column column-cb check-column">
                                <input type="checkbox" id="cb-select-all-1">
                            </td>
                            <th class="manage-column column-title"><?php _e('اسم المزاد', 'auction-system'); ?></th>
                            <th class="manage-column column-company"><?php _e('الشركة', 'auction-system'); ?></th>
                            <th class="manage-column column-date"><?php _e('تاريخ المزاد', 'auction-system'); ?></th>
                            <th class="manage-column column-city"><?php _e('المدينة', 'auction-system'); ?></th>
                            <th class="manage-column column-assets"><?php _e('الأصول', 'auction-system'); ?></th>
                            <th class="manage-column column-status"><?php _e('الحالة', 'auction-system'); ?></th>
                            <th class="manage-column column-actions"><?php _e('الإجراءات', 'auction-system'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($auctions_data['auctions'])): ?>
                            <?php foreach ($auctions_data['auctions'] as $auction): ?>
                                <tr>
                                    <th class="check-column">
                                        <input type="checkbox" name="auction_ids[]" value="<?php echo $auction->ID; ?>">
                                    </th>
                                    <td class="column-title">
                                        <strong>
                                            <a href="<?php echo get_edit_post_link($auction->ID); ?>">
                                                <?php echo esc_html($auction->post_title); ?>
                                            </a>
                                        </strong>
                                        <div class="row-actions">
                                            <span class="edit">
                                                <a href="<?php echo get_edit_post_link($auction->ID); ?>"><?php _e('تحرير', 'auction-system'); ?></a> |
                                            </span>
                                            <span class="view">
                                                <a href="<?php echo home_url('/?post_type=auction&p=' . $auction->ID); ?>" target="_blank"><?php _e('عرض', 'auction-system'); ?></a>
                                            </span>
                                        </div>
                                    </td>
                                    <td class="column-company">
                                        <?php echo esc_html(get_post_meta($auction->ID, '_company_name', true)); ?>
                                    </td>
                                    <td class="column-date">
                                        <?php
                                        $auction_date = get_post_meta($auction->ID, '_auction_date', true);
                                        $auction_time = get_post_meta($auction->ID, '_auction_time', true);
                                        if ($auction_date) {
                                            echo date_i18n('j F Y', strtotime($auction_date));
                                            if ($auction_time) {
                                                echo '<br><small>' . date_i18n('g:i A', strtotime($auction_time)) . '</small>';
                                            }
                                        }
                                        ?>
                                    </td>
                                    <td class="column-city">
                                        <?php echo esc_html(get_post_meta($auction->ID, '_auction_city', true)); ?>
                                    </td>
                                    <td class="column-assets">
                                        <?php echo $this->get_assets_count($auction->ID); ?>
                                    </td>
                                    <td class="column-status">
                                        <?php echo $this->get_status_badge($auction->post_status); ?>
                                    </td>
                                    <td class="column-actions">
                                        <div class="auction-actions">
                                            <?php if ($auction->post_status === 'pending'): ?>
                                                <button type="button" class="button button-small approve-auction" data-id="<?php echo $auction->ID; ?>">
                                                    <?php _e('اعتماد', 'auction-system'); ?>
                                                </button>
                                                <button type="button" class="button button-small reject-auction" data-id="<?php echo $auction->ID; ?>">
                                                    <?php _e('رفض', 'auction-system'); ?>
                                                </button>
                                            <?php elseif ($auction->post_status === 'publish'): ?>
                                                <button type="button" class="button button-small unapprove-auction" data-id="<?php echo $auction->ID; ?>">
                                                    <?php _e('إلغاء الاعتماد', 'auction-system'); ?>
                                                </button>
                                            <?php elseif ($auction->post_status === 'draft'): ?>
                                                <button type="button" class="button button-small reopen-auction" data-id="<?php echo $auction->ID; ?>">
                                                    <?php _e('إعادة للمراجعة', 'auction-system'); ?>
                                                </button>
                                            <?php endif; ?>
                                            <button type="button" class="button button-small button-link-delete delete-auction" data-id="<?php echo $auction->ID; ?>">
                                                <?php _e('حذف', 'auction-system'); ?>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="no-items">
                                    <?php _e('لا توجد مزادات', 'auction-system'); ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>

                <!-- Pagination -->
                <?php if ($auctions_data['total_pages'] > 1): ?>
                <div class="tablenav bottom">
                    <div class="tablenav-pages">
                        <?php
                        $current_page = $_GET['paged'] ?? 1;
                        $total_pages = $auctions_data['total_pages'];

                        echo paginate_links(array(
                            'base' => add_query_arg('paged', '%#%'),
                            'format' => '',
                            'current' => $current_page,
                            'total' => $total_pages,
                            'prev_text' => __('السابق', 'auction-system'),
                            'next_text' => __('التالي', 'auction-system')
                        ));
                        ?>
                    </div>
                </div>
                <?php endif; ?>
            </form>
        </div>

        <style>
        .auction-admin-dashboard .auction-stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0 30px 0;
        }

        .auction-admin-dashboard .stats-card {
            background: #fff;
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .auction-admin-dashboard .stats-card.pending { border-left: 4px solid #f39c12; }
        .auction-admin-dashboard .stats-card.approved { border-left: 4px solid #27ae60; }
        .auction-admin-dashboard .stats-card.rejected { border-left: 4px solid #e74c3c; }

        .auction-admin-dashboard .stats-icon {
            font-size: 24px;
            color: #666;
        }

        .auction-admin-dashboard .stats-content h3 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }

        .auction-admin-dashboard .stats-content p {
            margin: 5px 0 0 0;
            color: #666;
        }

        .auction-admin-dashboard .auction-filters {
            background: #fff;
            border: 1px solid #c3c4c7;
            padding: 15px;
            margin-bottom: 20px;
        }

        .auction-admin-dashboard .auction-filters form {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .auction-admin-dashboard .auction-actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .auction-admin-dashboard .unapprove-auction {
            background-color: #f39c12;
            border-color: #e67e22;
            color: white;
        }

        .auction-admin-dashboard .unapprove-auction:hover {
            background-color: #e67e22;
            border-color: #d35400;
        }

        .auction-admin-dashboard .reopen-auction {
            background-color: #3498db;
            border-color: #2980b9;
            color: white;
        }

        .auction-admin-dashboard .reopen-auction:hover {
            background-color: #2980b9;
            border-color: #1f618d;
        }

        .auction-admin-dashboard .status-badge {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .auction-admin-dashboard .status-pending { background: #fff3cd; color: #856404; }
        .auction-admin-dashboard .status-publish { background: #d4edda; color: #155724; }
        .auction-admin-dashboard .status-draft { background: #e2e3e5; color: #383d41; }
        </style>

        <script>
        var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
        jQuery(document).ready(function($) {
            // Handle approve auction
            $('.approve-auction').on('click', function() {
                var auctionId = $(this).data('id');
                console.log('Approving auction ID:', auctionId);
                if (confirm('<?php _e('هل أنت متأكد من اعتماد هذا المزاد؟', 'auction-system'); ?>')) {
                    $.post(ajaxurl, {
                        action: 'approve_auction',
                        auction_id: auctionId,
                        nonce: '<?php echo wp_create_nonce('auction_action'); ?>'
                    }, function(response) {
                        console.log('Approve response:', response);
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.data || 'حدث خطأ غير معروف');
                        }
                    }).fail(function(xhr, status, error) {
                        console.error('AJAX Error:', xhr.responseText);
                        alert('حدث خطأ في الاتصال: ' + error);
                    });
                }
            });

            // Handle reject auction
            $('.reject-auction').on('click', function() {
                var auctionId = $(this).data('id');
                console.log('Rejecting auction ID:', auctionId);
                if (confirm('<?php _e('هل أنت متأكد من رفض هذا المزاد؟', 'auction-system'); ?>')) {
                    $.post(ajaxurl, {
                        action: 'reject_auction',
                        auction_id: auctionId,
                        nonce: '<?php echo wp_create_nonce('auction_action'); ?>'
                    }, function(response) {
                        console.log('Reject response:', response);
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.data || 'حدث خطأ غير معروف');
                        }
                    }).fail(function(xhr, status, error) {
                        console.error('AJAX Error:', xhr.responseText);
                        alert('حدث خطأ في الاتصال: ' + error);
                    });
                }
            });

            // Handle unapprove auction
            $('.unapprove-auction').on('click', function() {
                var auctionId = $(this).data('id');
                console.log('Unapproving auction ID:', auctionId);
                if (confirm('<?php _e('هل أنت متأكد من إلغاء اعتماد هذا المزاد؟', 'auction-system'); ?>')) {
                    $.post(ajaxurl, {
                        action: 'unapprove_auction',
                        auction_id: auctionId,
                        nonce: '<?php echo wp_create_nonce('auction_action'); ?>'
                    }, function(response) {
                        console.log('Unapprove response:', response);
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.data || 'حدث خطأ غير معروف');
                        }
                    }).fail(function(xhr, status, error) {
                        console.error('AJAX Error:', xhr.responseText);
                        alert('حدث خطأ في الاتصال: ' + error);
                    });
                }
            });

            // Handle reopen auction
            $('.reopen-auction').on('click', function() {
                var auctionId = $(this).data('id');
                console.log('Reopening auction ID:', auctionId);
                if (confirm('<?php _e('هل أنت متأكد من إعادة هذا المزاد للمراجعة؟', 'auction-system'); ?>')) {
                    $.post(ajaxurl, {
                        action: 'reopen_auction',
                        auction_id: auctionId,
                        nonce: '<?php echo wp_create_nonce('auction_action'); ?>'
                    }, function(response) {
                        console.log('Reopen response:', response);
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.data || 'حدث خطأ غير معروف');
                        }
                    }).fail(function(xhr, status, error) {
                        console.error('AJAX Error:', xhr.responseText);
                        alert('حدث خطأ في الاتصال: ' + error);
                    });
                }
            });

            // Handle delete auction
            $('.delete-auction').on('click', function() {
                var auctionId = $(this).data('id');
                console.log('Deleting auction ID:', auctionId);
                if (confirm('<?php _e('هل أنت متأكد من حذف هذا المزاد؟ هذا الإجراء لا يمكن التراجع عنه.', 'auction-system'); ?>')) {
                    $.post(ajaxurl, {
                        action: 'delete_auction',
                        auction_id: auctionId,
                        nonce: '<?php echo wp_create_nonce('auction_action'); ?>'
                    }, function(response) {
                        console.log('Delete response:', response);
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.data || 'حدث خطأ غير معروف');
                        }
                    }).fail(function(xhr, status, error) {
                        console.error('AJAX Error:', xhr.responseText);
                        alert('حدث خطأ في الاتصال: ' + error);
                    });
                }
            });

            // Handle select all checkbox
            $('#cb-select-all-1').on('change', function() {
                $('input[name="auction_ids[]"]').prop('checked', this.checked);
            });
        });
        </script>
        <?php
    }

    /**
     * Get auctions data
     */
    private function get_auctions_data() {
        $paged = $_GET['paged'] ?? 1;
        $per_page = 20;
        $status = $_GET['status'] ?? '';
        $search = $_GET['search'] ?? '';

        $args = array(
            'post_type' => 'auction',
            'posts_per_page' => $per_page,
            'paged' => $paged,
            'meta_query' => array(),
            'orderby' => 'date',
            'order' => 'DESC'
        );

        // Add status filter
        if (!empty($status)) {
            $args['post_status'] = $status;
        } else {
            $args['post_status'] = array('pending', 'publish', 'draft');
        }

        // Add search
        if (!empty($search)) {
            $args['s'] = $search;
        }

        $query = new WP_Query($args);

        return array(
            'auctions' => $query->posts,
            'total_pages' => $query->max_num_pages,
            'total_items' => $query->found_posts
        );
    }

    /**
     * Get statistics
     */
    private function get_stats() {
        global $wpdb;

        $stats = array(
            'total' => 0,
            'pending' => 0,
            'approved' => 0,
            'rejected' => 0
        );

        $results = $wpdb->get_results("
            SELECT post_status, COUNT(*) as count
            FROM {$wpdb->posts}
            WHERE post_type = 'auction'
            GROUP BY post_status
        ");

        foreach ($results as $result) {
            $stats['total'] += $result->count;

            switch ($result->post_status) {
                case 'pending':
                    $stats['pending'] = $result->count;
                    break;
                case 'publish':
                    $stats['approved'] = $result->count;
                    break;
                case 'draft':
                    $stats['rejected'] = $result->count;
                    break;
            }
        }

        return $stats;
    }

    /**
     * Get assets count for auction
     */
    private function get_assets_count($auction_id) {
        global $wpdb;

        $assets_table = $wpdb->prefix . 'auction_assets';
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $assets_table WHERE auction_id = %d",
            $auction_id
        ));

        return intval($count);
    }

    /**
     * Get status badge
     */
    private function get_status_badge($status) {
        $badges = array(
            'pending' => array(
                'class' => 'status-pending',
                'text' => __('في انتظار المراجعة', 'auction-system')
            ),
            'publish' => array(
                'class' => 'status-publish',
                'text' => __('معتمد', 'auction-system')
            ),
            'draft' => array(
                'class' => 'status-draft',
                'text' => __('مسودة', 'auction-system')
            )
        );

        $badge = $badges[$status] ?? array('class' => 'status-draft', 'text' => $status);

        return sprintf(
            '<span class="status-badge %s">%s</span>',
            $badge['class'],
            $badge['text']
        );
    }

    /**
     * Handle bulk actions
     */
    private function handle_bulk_actions() {
        if (!isset($_POST['auction_bulk_nonce']) || !wp_verify_nonce($_POST['auction_bulk_nonce'], 'auction_bulk_action')) {
            return;
        }

        if (!current_user_can('edit_posts')) {
            return;
        }

        $action = $_POST['action'];
        $auction_ids = $_POST['auction_ids'] ?? array();

        if (empty($auction_ids)) {
            return;
        }

        foreach ($auction_ids as $auction_id) {
            switch ($action) {
                case 'approve':
                    wp_update_post(array(
                        'ID' => $auction_id,
                        'post_status' => 'publish'
                    ));
                    break;
                case 'unapprove':
                    wp_update_post(array(
                        'ID' => $auction_id,
                        'post_status' => 'pending'
                    ));
                    break;
                case 'reject':
                    wp_update_post(array(
                        'ID' => $auction_id,
                        'post_status' => 'draft'
                    ));
                    break;
                case 'reopen':
                    wp_update_post(array(
                        'ID' => $auction_id,
                        'post_status' => 'pending'
                    ));
                    break;
                case 'delete':
                    wp_delete_post($auction_id, true);
                    $this->delete_auction_assets($auction_id);
                    break;
            }
        }

        // Redirect to avoid resubmission
        wp_redirect(add_query_arg(array('message' => 'bulk_action_completed'), remove_query_arg(array('action', 'auction_ids', 'auction_bulk_nonce'))));
        exit;
    }

    /**
     * Approve auction via AJAX
     */
    public function approve_auction() {
        // Log the request for debugging
        error_log('Approve auction AJAX called with data: ' . print_r($_POST, true));

        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'auction_action')) {
            error_log('Nonce verification failed for approve_auction');
            wp_send_json_error(__('خطأ في التحقق من الأمان', 'auction-system'));
        }

        if (!current_user_can('edit_posts')) {
            error_log('User does not have edit_posts capability');
            wp_send_json_error(__('ليس لديك صلاحية لهذا الإجراء', 'auction-system'));
        }

        $auction_id = intval($_POST['auction_id']);

        if (!$auction_id) {
            error_log('Invalid auction ID provided');
            wp_send_json_error(__('معرف المزاد غير صحيح', 'auction-system'));
        }

        $result = wp_update_post(array(
            'ID' => $auction_id,
            'post_status' => 'publish'
        ));

        if ($result && !is_wp_error($result)) {
            error_log('Auction approved successfully: ' . $auction_id);
            wp_send_json_success(__('تم اعتماد المزاد بنجاح', 'auction-system'));
        } else {
            error_log('Failed to approve auction: ' . $auction_id . ' - Error: ' . (is_wp_error($result) ? $result->get_error_message() : 'Unknown error'));
            wp_send_json_error(__('فشل في اعتماد المزاد', 'auction-system'));
        }
    }

    /**
     * Reject auction via AJAX
     */
    public function reject_auction() {
        // Log the request for debugging
        error_log('Reject auction AJAX called with data: ' . print_r($_POST, true));

        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'auction_action')) {
            error_log('Nonce verification failed for reject_auction');
            wp_send_json_error(__('خطأ في التحقق من الأمان', 'auction-system'));
        }

        if (!current_user_can('edit_posts')) {
            error_log('User does not have edit_posts capability');
            wp_send_json_error(__('ليس لديك صلاحية لهذا الإجراء', 'auction-system'));
        }

        $auction_id = intval($_POST['auction_id']);

        if (!$auction_id) {
            error_log('Invalid auction ID provided');
            wp_send_json_error(__('معرف المزاد غير صحيح', 'auction-system'));
        }

        $result = wp_update_post(array(
            'ID' => $auction_id,
            'post_status' => 'draft'
        ));

        if ($result && !is_wp_error($result)) {
            error_log('Auction rejected successfully: ' . $auction_id);
            wp_send_json_success(__('تم رفض المزاد', 'auction-system'));
        } else {
            error_log('Failed to reject auction: ' . $auction_id . ' - Error: ' . (is_wp_error($result) ? $result->get_error_message() : 'Unknown error'));
            wp_send_json_error(__('فشل في رفض المزاد', 'auction-system'));
        }
    }

    /**
     * Unapprove auction via AJAX
     */
    public function unapprove_auction() {
        // Log the request for debugging
        error_log('Unapprove auction AJAX called with data: ' . print_r($_POST, true));

        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'auction_action')) {
            error_log('Nonce verification failed for unapprove_auction');
            wp_send_json_error(__('خطأ في التحقق من الأمان', 'auction-system'));
        }

        if (!current_user_can('edit_posts')) {
            error_log('User does not have edit_posts capability');
            wp_send_json_error(__('ليس لديك صلاحية لهذا الإجراء', 'auction-system'));
        }

        $auction_id = intval($_POST['auction_id']);

        if (!$auction_id) {
            error_log('Invalid auction ID provided');
            wp_send_json_error(__('معرف المزاد غير صحيح', 'auction-system'));
        }

        $result = wp_update_post(array(
            'ID' => $auction_id,
            'post_status' => 'pending'
        ));

        if ($result && !is_wp_error($result)) {
            error_log('Auction unapproved successfully: ' . $auction_id);
            wp_send_json_success(__('تم إلغاء اعتماد المزاد بنجاح', 'auction-system'));
        } else {
            error_log('Failed to unapprove auction: ' . $auction_id . ' - Error: ' . (is_wp_error($result) ? $result->get_error_message() : 'Unknown error'));
            wp_send_json_error(__('فشل في إلغاء اعتماد المزاد', 'auction-system'));
        }
    }

    /**
     * Reopen auction for review via AJAX
     */
    public function reopen_auction() {
        // Log the request for debugging
        error_log('Reopen auction AJAX called with data: ' . print_r($_POST, true));

        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'auction_action')) {
            error_log('Nonce verification failed for reopen_auction');
            wp_send_json_error(__('خطأ في التحقق من الأمان', 'auction-system'));
        }

        if (!current_user_can('edit_posts')) {
            error_log('User does not have edit_posts capability');
            wp_send_json_error(__('ليس لديك صلاحية لهذا الإجراء', 'auction-system'));
        }

        $auction_id = intval($_POST['auction_id']);

        if (!$auction_id) {
            error_log('Invalid auction ID provided');
            wp_send_json_error(__('معرف المزاد غير صحيح', 'auction-system'));
        }

        $result = wp_update_post(array(
            'ID' => $auction_id,
            'post_status' => 'pending'
        ));

        if ($result && !is_wp_error($result)) {
            error_log('Auction reopened successfully: ' . $auction_id);
            wp_send_json_success(__('تم إعادة المزاد للمراجعة بنجاح', 'auction-system'));
        } else {
            error_log('Failed to reopen auction: ' . $auction_id . ' - Error: ' . (is_wp_error($result) ? $result->get_error_message() : 'Unknown error'));
            wp_send_json_error(__('فشل في إعادة المزاد للمراجعة', 'auction-system'));
        }
    }

    /**
     * Delete auction via AJAX
     */
    public function delete_auction() {
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'auction_action')) {
            wp_send_json_error(__('خطأ في التحقق من الأمان', 'auction-system'));
        }

        if (!current_user_can('edit_posts')) {
            wp_send_json_error(__('ليس لديك صلاحية لهذا الإجراء', 'auction-system'));
        }

        $auction_id = intval($_POST['auction_id']);

        // Delete auction assets first
        $this->delete_auction_assets($auction_id);

        // Delete auction post
        $result = wp_delete_post($auction_id, true);

        if ($result) {
            wp_send_json_success(__('تم حذف المزاد بنجاح', 'auction-system'));
        } else {
            wp_send_json_error(__('فشل في حذف المزاد', 'auction-system'));
        }
    }

    /**
     * Delete auction assets
     */
    private function delete_auction_assets($auction_id) {
        global $wpdb;

        $assets_table = $wpdb->prefix . 'auction_assets';
        $wpdb->delete($assets_table, array('auction_id' => $auction_id));
    }
}
