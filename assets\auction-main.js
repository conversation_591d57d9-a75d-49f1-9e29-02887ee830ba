/**
 * Unified Auction System JavaScript
 * 
 * @package AuctionSystem
 * @version 2.1.0
 * @description Unified JavaScript for the auction system frontend with Lazy Loading
 */

(function($) {
    'use strict';

    // Global variables
    let countdownTimer = null;
    let lazyLoading = false;
    let loadingMore = false;
    let currentPage = 1;
    let totalPages = 1;
    let ajaxInProgress = false;
    let lazySettings = {
        enabled: true,
        autoLoad: true,
        itemsPerPage: 10,
        animation: 'fade',
        scrollDistance: 300
    };

    /**
     * Initialize the auction system
     */
    function initializeAuctionSystem() {
        // Get lazy loading settings from localized data
        if (typeof auctionData !== 'undefined' && auctionData.lazy_settings) {
            lazySettings = {
                enabled: Boolean(auctionData.lazy_settings.lazy_load_enabled),
                autoLoad: Boolean(auctionData.lazy_settings.lazy_load_auto_load),
                itemsPerPage: parseInt(auctionData.lazy_settings.lazy_load_items_per_page) || 10,
                animation: auctionData.lazy_settings.lazy_load_animation || 'fade',
                scrollDistance: parseInt(auctionData.lazy_settings.lazy_load_scroll_distance) || 300
            };
            
            currentPage = parseInt(auctionData.current_page) || 1;
            totalPages = parseInt(auctionData.max_pages) || 1;
        }

        // Initialize basic functionality
        initBasicFunctionality();
        
        // Check if we already have assets loaded
        const hasAssets = $('#auction-assets-grid .auction-asset').length > 0;
        
        // Only initialize lazy loading if enabled and we don't have assets yet
        if (lazySettings.enabled && !hasAssets) {
            initLazyLoading();
        } else {
            // If we have assets, just update the load more button
            updateLoadMoreButton();
        }
        
        // Initialize other components
        initGallery();
        initCountdown();
        initInteractiveElements();
        initAuctionDescription();
    }

    /**
     * Initialize basic functionality
     */
    function initBasicFunctionality() {
        // Add any basic functionality here
    }

    /**
     * Initialize lazy loading functionality
     */
    function initLazyLoading() {
        if (!lazySettings.enabled) {
            return;
        }
        
        lazyLoading = true;
        
        // Set up manual load more button click handler
        $('#load-more-assets').on('click', function(e) {
            e.preventDefault();
            if (!loadingMore && currentPage < totalPages) {
                loadMoreAssets();
            }
        });
        
        // Set up auto-loading on scroll if enabled
        if (lazySettings.autoLoad) {
            setupAutoLoad();
        }
    }

    /**
     * Set up auto-load on scroll with throttling
     */
    function setupAutoLoad() {
        let scrollTimeout;
        
        $(window).on('scroll', function() {
            clearTimeout(scrollTimeout);
            
            // Throttle the scroll event
            scrollTimeout = setTimeout(function() {
                if (loadingMore || currentPage >= totalPages || ajaxInProgress) {
                    return;
                }
                
                const $lastAsset = $('#auction-assets-grid .auction-asset').last();
                if ($lastAsset.length) {
                    const lastAssetOffset = $lastAsset.offset().top + $lastAsset.outerHeight();
                    const windowBottom = $(window).scrollTop() + $(window).height();
                    if (windowBottom + lazySettings.scrollDistance >= lastAssetOffset) {
                        loadMoreAssets();
                    }
                }
            }, 200);
        });
    }

    /**
     * Load more assets via AJAX
     */
    function loadMoreAssets() {
        console.log('loadMoreAssets called', {
            loadingMore: loadingMore,
            ajaxInProgress: ajaxInProgress,
            currentPage: currentPage,
            totalPages: totalPages
        });
        
        if (loadingMore || ajaxInProgress || currentPage >= totalPages) {
            console.log('Skipping loadMoreAssets - Condition not met');
            return;
        }
        
        loadingMore = true;
        ajaxInProgress = true;
        
        // Show loading indicator
        const $loadingIndicator = $('#loading-indicator');
        $loadingIndicator.fadeIn(200);
        
        // Make AJAX request
        $.ajax({
            url: auctionData.ajax_url,
            type: 'POST',
            data: {
                action: 'load_more_assets',
                auction_id: auctionData.auction_id,
                page: currentPage + 1,
                per_page: lazySettings.itemsPerPage,
                nonce: auctionData.nonce
            },
            success: function(response) {
                if (response.success && response.data && response.data.html) {
                    // Update current page
                    currentPage = response.data.current_page;
                    totalPages = response.data.total_pages;
                    
                    // Append new assets with animation
                    const $newAssets = $(response.data.html);
                    
                    // Apply animation based on settings
                    if (lazySettings.animation === 'fade') {
                        $newAssets.hide().appendTo('#auction-assets-grid').fadeIn(600);
                    } else if (lazySettings.animation === 'slide') {
                        $newAssets.hide().appendTo('#auction-assets-grid').slideDown(600);
                    } else {
                        $newAssets.appendTo('#auction-assets-grid');
                    }
                    
                    // Update load more button visibility
                    updateLoadMoreButton();
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading more assets:', error);
                // Show error message
                const $errorMsg = $('<div class="error-message" style="color: #dc3545; text-align: center; padding: 10px;">حدث خطأ أثناء تحميل المزيد من الأصول. يرجى المحاولة مرة أخرى.</div>');
                $('#auction-assets-grid').append($errorMsg);
                setTimeout(() => $errorMsg.fadeOut(1000, () => $errorMsg.remove()), 3000);
            },
            complete: function() {
                loadingMore = false;
                ajaxInProgress = false;
                $loadingIndicator.fadeOut(200);
            }
        });
    }
    
    /**
     * Update load more button visibility
     */
    function updateLoadMoreButton() {
        const $loadMoreBtn = $('#load-more-assets');
        if ($loadMoreBtn.length) {
            if (currentPage >= totalPages) {
                $loadMoreBtn.fadeOut(300, function() {
                    $(this).remove();
                });
            } else if (!lazySettings.autoLoad) {
                $loadMoreBtn.fadeIn(300);
            }
        }
    }

    /**
     * Initialize gallery functionality
     */
    function initGallery() {
        $('.asset-gallery').on('click', '.thumbnail', function() {
            const $thumb = $(this);
            const $gallery = $thumb.closest('.asset-gallery');
            const $mainImage = $gallery.find('.main-image');
            const newSrc = $thumb.data('full');
            
            // Update main image
            $mainImage.fadeOut(200, function() {
                $mainImage.attr('src', newSrc).fadeIn(200);
            });
            
            // Update active thumbnail
            $gallery.find('.thumbnail').removeClass('active');
            $thumb.addClass('active');
            
            return false;
        });
    }

    /**
     * Initialize countdown timer
     */
    function initCountdown() {
        const $countdown = $('.auction-countdown');
        if (!$countdown.length) {
            console.error('Countdown element not found');
            return;
        }
        
        // Get end time from data-countdown-target attribute
        let endTimeStr = $countdown.data('countdown-target');
        if (!endTimeStr) {
            console.error('Countdown target time not found');
            return;
        }
        
        // Parse the ISO 8601 date string to timestamp
        const endTime = new Date(endTimeStr).getTime();
        
        if (isNaN(endTime)) {
            console.error('Invalid end time format:', endTimeStr);
            return;
        }
        
        // Clear any existing interval to prevent multiple timers
        if (countdownTimer) {
            clearInterval(countdownTimer);
        }
        
        function updateCountdown() {
            const now = new Date().getTime();
            const distance = endTime - now;
            
            if (distance < 0) {
                clearInterval(countdownTimer);
                $countdown.html(`
                    <div class="countdown-container">
                        <div class="countdown-expired">
                            <span class="expired-icon">⏰</span>
                            <span class="expired-text">انتهى المزاد</span>
                        </div>
                    </div>
                `);
                
                // Trigger any end of auction events
                $(document).trigger('auction:ended');
                return;
            }
            
            // Calculate time units
            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);
            
            // Create countdown HTML with RTL support
            let countdownHTML = `
                <div class="countdown-container">
                    <div class="countdown-item">
                        <span class="countdown-number">${days}</span>
                        <span class="countdown-label">أيام</span>
                    </div>
                    <div class="countdown-separator">:</div>
                    <div class="countdown-item">
                        <span class="countdown-number">${hours.toString().padStart(2, '0')}</span>
                        <span class="countdown-label">ساعات</span>
                    </div>
                    <div class="countdown-separator">:</div>
                    <div class="countdown-item">
                        <span class="countdown-number">${minutes.toString().padStart(2, '0')}</span>
                        <span class="countdown-label">دقائق</span>
                    </div>
                    <div class="countdown-separator">:</div>
                    <div class="countdown-item">
                        <span class="countdown-number">${seconds.toString().padStart(2, '0')}</span>
                        <span class="countdown-label">ثواني</span>
                    </div>
                </div>`;
            
            // Update the countdown element
            $countdown.html(countdownHTML);
            
            // Trigger an event for other scripts to hook into
            $(document).trigger('auction:countdownUpdate', {
                days: days,
                hours: hours,
                minutes: minutes,
                seconds: seconds,
                distance: distance
            });
        }
        
        // Update immediately and then every second
        updateCountdown();
        countdownTimer = setInterval(updateCountdown, 1000);
        
        // Make sure to clear interval when page is unloaded
        $(window).on('unload', function() {
            if (countdownTimer) {
                clearInterval(countdownTimer);
            }
        });
    }

    /**
     * Initialize auction description toggle
     */
    function initAuctionDescription() {
        $(document).on('click', '.description-toggle', function(e) {
            e.preventDefault();
            const $toggle = $(this);
            const $content = $toggle.next('.description-content');
            const $arrow = $toggle.find('.toggle-arrow');
            
            $content.slideToggle(300, function() {
                const isExpanded = $toggle.attr('aria-expanded') === 'true';
                $toggle.attr('aria-expanded', !isExpanded);
                $arrow.toggleClass('is-expanded');
            });
        });
    }
    
    /**
     * Initialize interactive elements
     */
    function initInteractiveElements() {
        // Add any interactive elements here
    }

    // Initialize when document is ready
    $(document).ready(function() {
        initializeAuctionSystem();
    });

})(jQuery);
