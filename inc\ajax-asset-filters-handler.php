<?php
/**
 * AJAX Handler for Asset Filters
 *
 * @package AuctionSystem
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Include required files
require_once get_stylesheet_directory() . '/inc/auction-components/asset-gallery.php';
require_once get_stylesheet_directory() . '/templates/asset-card-template.php';

// Include AuctionSettings class if not exists
if (!class_exists('AuctionSettings')) {
    require_once get_stylesheet_directory() . '/inc/auction-settings.php';
}

add_action('wp_ajax_filter_auction_assets', 'auction_system_filter_auction_assets_callback');
add_action('wp_ajax_nopriv_filter_auction_assets', 'auction_system_filter_auction_assets_callback');

function auction_system_filter_auction_assets_callback() {
    try {
        // Verify nonce
        if (!check_ajax_referer('asset_filters_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => 'Invalid security token']);
            return;
        }

        global $wpdb;
        $assets_table_name = $wpdb->prefix . 'auction_assets';
        $asset_types_table_name = $wpdb->prefix . 'auction_asset_types';

        // Get AuctionSettings instance
        $auction_settings = AuctionSettings::get_instance();
        $settings = $auction_settings->get_all_settings();
        
        // Get lazy load settings
        $items_per_page = isset($settings['lazy_load_per_page']) ? (int)$settings['lazy_load_per_page'] : 10;
        $current_page = isset($_POST['page']) ? intval($_POST['page']) : 1;
        $offset = ($current_page - 1) * $items_per_page;

        // Fetch all active asset type names once for mapping
        $active_asset_types = array();
        if ($wpdb->get_var("SHOW TABLES LIKE '$asset_types_table_name'") == $asset_types_table_name) {
            $types = $wpdb->get_results("SELECT id, name FROM {$asset_types_table_name} WHERE is_active = 1");
            if ($types) {
                foreach ($types as $type) {
                    $active_asset_types[$type->id] = $type->name;
                }
            }
        }

        $auction_id = isset($_POST['auction_id']) ? intval($_POST['auction_id']) : 0;
        $is_reset = isset($_POST['is_reset']) && $_POST['is_reset'] === '1';

        if (!$auction_id) {
            wp_send_json_error(['message' => __('Auction ID is missing.', 'auction-system')]);
            return;
        }

        // --- Start building the SQL query ---
        $base_sql_from_where = "FROM {$assets_table_name} WHERE auction_id = %d";
        $params_for_where = [$auction_id];
        $where_clauses_additional = "";

        if (!$is_reset) {
            // Deed Number Filter
            if (!empty($_POST['filter_deed_number'])) {
                $where_clauses_additional .= " AND deed_number LIKE %s";
                $params_for_where[] = '%' . $wpdb->esc_like(sanitize_text_field($_POST['filter_deed_number'])) . '%';
            }

            // District Filter
            if (!empty($_POST['filter_district'])) {
                $where_clauses_additional .= " AND LOWER(district) LIKE LOWER(%s)";
                $params_for_where[] = '%' . $wpdb->esc_like(sanitize_text_field($_POST['filter_district'])) . '%';
            }

            // Area Filter
            $area_min = isset($_POST['filter_area_min']) && is_numeric($_POST['filter_area_min']) && $_POST['filter_area_min'] !== '' ? floatval($_POST['filter_area_min']) : null;
            $area_max = isset($_POST['filter_area_max']) && is_numeric($_POST['filter_area_max']) && $_POST['filter_area_max'] !== '' ? floatval($_POST['filter_area_max']) : null;

            if ($area_min !== null && $area_max !== null && $area_max >= $area_min) {
                $where_clauses_additional .= " AND area BETWEEN %f AND %f";
                $params_for_where[] = $area_min;
                $params_for_where[] = $area_max;
            } elseif ($area_min !== null && $area_min >= 0) {
                $where_clauses_additional .= " AND area >= %f";
                $params_for_where[] = $area_min;
            } elseif ($area_max !== null && $area_max > 0) {
                $where_clauses_additional .= " AND area <= %f";
                $params_for_where[] = $area_max;
            }
            
            // Asset Type Filter
            if (!empty($_POST['filter_asset_type_id'])) {
                $filter_asset_type_id = absint($_POST['filter_asset_type_id']);
                if ($filter_asset_type_id > 0) {
                    $where_clauses_additional .= " AND asset_type_id = %d";
                    $params_for_where[] = $filter_asset_type_id;
                }
            }
        }
        
        // Construct the full WHERE part
        $full_where_clause = $base_sql_from_where . $where_clauses_additional;

        // Get total count of matching assets
        $count_sql = "SELECT COUNT(*) " . $full_where_clause;
        $total_found = $wpdb->get_var($wpdb->prepare($count_sql, $params_for_where));

        // Get the assets with pagination
        $assets_sql = "SELECT * " . $full_where_clause . " ORDER BY sort_order ASC LIMIT %d OFFSET %d";
        $params_for_query = array_merge($params_for_where, [$items_per_page, $offset]);
        $assets = $wpdb->get_results($wpdb->prepare($assets_sql, $params_for_query));

        ob_start();
        if ($assets) {
            foreach ($assets as $index => $asset) {
                // Process asset data
                $asset_data = (array)$asset;
                $asset_data['images'] = json_decode($asset->images, true);
                $asset_data['asset_type'] = isset($active_asset_types[$asset->asset_type_id]) ? $active_asset_types[$asset->asset_type_id] : '';
                
                // Use the unified asset card template
                echo get_unified_asset_card($asset_data, $index);
            }
        }
        $html = ob_get_clean();

        // Calculate total pages
        $total_pages = ceil($total_found / $items_per_page);

        wp_send_json_success([
            'count' => $total_found,
            'html' => $html,
            'current_page' => $current_page,
            'total_pages' => $total_pages,
            'has_more' => $current_page < $total_pages
        ]);

    } catch (Exception $e) {
        error_log('Asset Filter Error: ' . $e->getMessage());
        wp_send_json_error([
            'message' => 'An error occurred while processing your request.',
            'debug' => WP_DEBUG ? $e->getMessage() : null
        ]);
    }
}