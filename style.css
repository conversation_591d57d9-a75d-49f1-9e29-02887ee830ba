/*
 Theme Name:   GeneratePress Child
 Theme URI:    https://generatepress.com
 Description:  Default GeneratePress child theme
 Author:       <PERSON>
 Author URI:   https://tomusborne.com
 Template:     generatepress
 Version:      0.1
*/

/* GeneratePress Site CSS */ /* Top Menu */
.menu-bar-items {
	flex-direction: row-reverse;
	margin-right:5px !important;
}
/* Header alignment */
@media(min-width: 669px) {
	.inside-navigation .navigation-branding {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
    }
	.nav-align-right .inside-navigation {
		justify-content: space-between;
	}
}
/* Search results page header style */
.search-results .page-header .page-title {
	font-size: 36px;
	text-transform: uppercase;
	border-bottom: 2px solid var(--contrast);
}
/* Default theme button */
button,
.form-submit .submit {
	padding-top: 16px;
	padding-bottom: 16px;
}
/* Sidebar */
.sidebar {
	margin-top: 40px;
	margin-left: 0;
}
@media(min-width: 768px) {
	.sidebar {
		margin-left: 40px;
		margin-top: 0;
	}
}
/* Single post - Sticky Related Posts sidebar */
@media(min-width: 769px) {
	.is-sticky {
			position: sticky;
			top: 112px;
	}
}
/* Search modal */
.gp-modal__container {
	overflow-y: auto;
}
.gp-search-modal .gp-modal__overlay {
    padding-top: 15vh;
}
/* Tag cloud */
.wp-block-tag-cloud {
	display: flex;
	flex-wrap: wrap;
	row-gap: 10px;
}
.wp-block-tag-cloud a {
	border-radius: 3px;
	padding: 4px 10px;
}
.site-footer .wp-block-tag-cloud a {
	background-color: var(--contrast);
	color: var(--base);
}
.sidebar .wp-block-tag-cloud a {
	background-color: var(--base);
	color: var(--contrast);
	border: 1px solid var(--contrast);
}
.wp-block-tag-cloud a:hover {
	background-color: var(--base);
	color: var(--contrast);
}
.sidebar a:hover {
	background-color: var(--contrast);
	color: var(--base);
} /* End GeneratePress Site CSS */


/* Top Menu */
.menu-bar-items {
	flex-direction: row-reverse;
}
/* Header alignment */
@media(min-width: 669px) {
	.inside-navigation .navigation-branding {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
    }
	.nav-align-right .inside-navigation {
		justify-content: space-between;
	}
	
}
/* Search results page header style */
.search-results .page-header .page-title {
	font-size: 36px;
	text-transform: uppercase;
	border-bottom: 2px solid var(--contrast);
}
/* Default theme button */
button,
.form-submit .submit {
	padding-top: 16px;
	padding-bottom: 16px;
}
/* Sidebar */
.sidebar {
	margin-top: 40px;
	margin-left: 0;
}
@media(min-width: 768px) {
	.sidebar {
		margin-left: 40px;
		margin-top: 0;
	}
}
/* Single post - Sticky Related Posts sidebar */
@media(min-width: 769px) {

	.is-sticky {
			position: sticky;
			top: 112px;
	}
}
/* Search modal */
.gp-modal__container {
	overflow-y: auto;
}
.gp-search-modal .gp-modal__overlay {
    padding-top: 15vh;
}
/* Tag cloud */
.wp-block-tag-cloud {
	display: flex;
	flex-wrap: wrap;
	row-gap: 10px;
}
.wp-block-tag-cloud a {
	border-radius: 3px;
	padding: 4px 10px;
}
.site-footer .wp-block-tag-cloud a {
	background-color: var(--contrast);
	color: var(--base);
}
.sidebar .wp-block-tag-cloud a {
	background-color: var(--base);
	color: var(--contrast);
	border: 1px solid var(--contrast);
}
.wp-block-tag-cloud a:hover {
	background-color: var(--base);
	color: var(--contrast);
}
.sidebar a:hover {
	background-color: var(--contrast);
	color: var(--base);
} 
/* End GeneratePress Site CSS */
.li-separetd li {
    border-bottom: 2px dashed;
    margin-bottom: 15px;
}
.main-navigation a{
	color: black !important;
}
.main-navigation a:hover{
	color:var(--accent) !important;
}
#secondary-navigation{
	border-bottom:solid 1px #f0f0f0;
}
#menu-sec > .menu-item > a{
	font-size:16px;
		font-weight:500;
}
ul {
    margin: 0 0 .5em .5em;
}
.wp-block-latest-posts__post-author, .wp-block-latest-posts__post-date {
    display: block;
    font-size: .8125em;
    color: #D61935;
}
.long-title{
	overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}
.mb-slider{
	display:none;
}
@media (max-width:769px){

	#secondary-navigation {
		border-bottom: solid 1px #f0f0f0;
		display: none !important;
	}
	.mb-slider{
		display:block;
		margin-bottom : 15px;
	}
	.ultp-block-0c9567 .ultp-block-content-inner{
		padding:10px 10px 10px 10px;
		margin-bottom:100px;
		width:90%;
		background-color:#ffffffa6
	}
	.ultp-block-0c9567 .ultp-block-content-middlePosition{
		align-items:flex-end;
	}
	
	.one-container .site-content {
        padding: 10px 0px 40px 0px;
    }

}
.separator--dotted {
    background: linear-gradient(90deg, #ffffff 2px, #0000 1%) 50%, linear-gradient(#ffffff 2px, #0000 1%) 50%, #000;
    background-position: 0 0;
    background-size: 3px 3px;
    height: 9px;
    width: 100%;
}
.entry-content .alignwide {
    margin-left: -40px;
    width: 100%;
    max-width: calc(100% + 80px);
}
.blockspare-banner-wrapper {
    --gap: 10px;
    margin-top: 0px !important;
    margin-Bottom: 30px;
    margin-left: 0px;
    margin-right: 0px;
}
.secondary-navigation .main-nav ul li a {
    font-family: inherit;
    font-weight: 700 !important;
    text-transform: none;
    font-size: 13px;
    padding-left: 20px;
    padding-right: 20px;
    line-height: 40px;
}

#generate-slideout-menu .slideout-menu li {
    float: none;
    width: 100%;
    clear: both;
    text-align: right;
}
.prives-list {
    display: flex;
    gap: 15px;
    flex-direction: column;
}

@media(max-width: 767px) {
    .separator--dotted {
        background: linear-gradient(90deg, #fffdfd 2px, #0000 1%) 50%, linear-gradient(#ffffff 2px, #0000 1%) 50%, #000;
        background-position: 0 0;
        background-size: 3px 3px;
        height: 9px;
        width: 90%;
    }

}

#right-sidebar {
    order: -10;
    border-left: dashed 2px !important;
    margin-left: 0px !important;
}
