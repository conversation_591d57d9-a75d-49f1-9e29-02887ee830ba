<?php
/**
 * Auction Asset Types Management
 *
 * @package AuctionSystem
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Auction Asset Types Class
 */
class AuctionAssetTypes {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_ajax_add_asset_type', array($this, 'add_asset_type'));
        add_action('wp_ajax_edit_asset_type', array($this, 'edit_asset_type'));
        add_action('wp_ajax_delete_asset_type', array($this, 'delete_asset_type'));
        add_action('wp_ajax_toggle_asset_type', array($this, 'toggle_asset_type'));
    }

    /**
     * Render page
     */
    public function render() {
        // Handle form submissions
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handle_form_submission();
        }

        // Get asset types
        $asset_types = $this->get_asset_types();

        ?>
        <div class="wrap auction-asset-types-page">
            <div class="auction-page-header">
                <div class="wrap">
                    <div class="auction-page-title">
                        <h1>
                            <span class="dashicons dashicons-category"></span>
                            <?php _e('إدارة أنواع الأصول', 'auction-system'); ?>
                        </h1>
                        <p class="auction-page-description">
                            <?php _e('إدارة وتنظيم أنواع الأصول المختلفة في المزادات', 'auction-system'); ?>
                        </p>
                    </div>
                    <div class="auction-page-actions">
                        <button type="button" class="button button-primary add-new-asset-type">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php _e('إضافة نوع جديد', 'auction-system'); ?>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="auction-stats-overview">
                <div class="stat-card">
                    <div class="stat-icon">
                        <span class="dashicons dashicons-category"></span>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo count($asset_types); ?></h3>
                        <p><?php _e('إجمالي الأنواع', 'auction-system'); ?></p>
                    </div>
                </div>
                <div class="stat-card active">
                    <div class="stat-icon">
                        <span class="dashicons dashicons-yes-alt"></span>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo count(array_filter($asset_types, function($type) { return $type->is_active; })); ?></h3>
                        <p><?php _e('الأنواع النشطة', 'auction-system'); ?></p>
                    </div>
                </div>
                <div class="stat-card inactive">
                    <div class="stat-icon">
                        <span class="dashicons dashicons-dismiss"></span>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo count(array_filter($asset_types, function($type) { return !$type->is_active; })); ?></h3>
                        <p><?php _e('الأنواع غير النشطة', 'auction-system'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Add New Asset Type Form (Hidden by default) -->
            <div id="add-asset-type-form" class="auction-form-container" style="display: none;">
                <div class="auction-form-header">
                    <h2>
                        <span class="dashicons dashicons-plus-alt"></span>
                        <?php _e('إضافة نوع أصل جديد', 'auction-system'); ?>
                    </h2>
                    <button type="button" class="close-form">
                        <span class="dashicons dashicons-no-alt"></span>
                    </button>
                </div>

                <form method="post" class="auction-form">
                    <?php wp_nonce_field('asset_type_action', 'asset_type_nonce'); ?>
                    <input type="hidden" name="action" value="add">

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="asset_type_name" class="form-label">
                                <?php _e('اسم النوع', 'auction-system'); ?>
                                <span class="required">*</span>
                            </label>
                            <input type="text" id="asset_type_name" name="name" class="form-input" required
                                   placeholder="<?php _e('مثال: أراضي سكنية، عقارات تجارية', 'auction-system'); ?>">
                        </div>

                        <div class="form-group">
                            <label for="asset_type_sort_order" class="form-label">
                                <?php _e('ترتيب العرض', 'auction-system'); ?>
                            </label>
                            <input type="number" id="asset_type_sort_order" name="sort_order" class="form-input"
                                   value="0" min="0" placeholder="0">
                            <small class="form-help"><?php _e('0 = الأول في القائمة', 'auction-system'); ?></small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="asset_type_description" class="form-label">
                            <?php _e('الوصف', 'auction-system'); ?>
                        </label>
                        <textarea id="asset_type_description" name="description" class="form-textarea" rows="3"
                                  placeholder="<?php _e('وصف اختياري لنوع الأصل...', 'auction-system'); ?>"></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-checkbox">
                            <input type="checkbox" id="asset_type_active" name="is_active" value="1" checked>
                            <span class="checkmark"></span>
                            <?php _e('نشط ومتاح للاستخدام', 'auction-system'); ?>
                        </label>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="button button-secondary close-form">
                            <?php _e('إلغاء', 'auction-system'); ?>
                        </button>
                        <button type="submit" class="button button-primary">
                            <span class="dashicons dashicons-saved"></span>
                            <?php _e('حفظ النوع الجديد', 'auction-system'); ?>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Asset Types List -->
            <div class="auction-content-container">
                <div class="auction-content-header">
                    <h2>
                        <span class="dashicons dashicons-list-view"></span>
                        <?php _e('أنواع الأصول الموجودة', 'auction-system'); ?>
                    </h2>
                    <div class="content-actions">
                        <span class="items-count"><?php echo count($asset_types); ?> <?php _e('نوع', 'auction-system'); ?></span>
                    </div>
                </div>

                <?php if (!empty($asset_types)): ?>
                    <div class="asset-types-grid">
                        <?php foreach ($asset_types as $type): ?>
                            <div class="asset-type-card" data-id="<?php echo $type->id; ?>">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3><?php echo esc_html($type->name); ?></h3>
                                        <div class="card-meta">
                                            <span class="sort-order"><?php _e('ترتيب:', 'auction-system'); ?> <?php echo intval($type->sort_order); ?></span>
                                            <span class="usage-count"><?php echo $this->get_usage_count($type->id); ?> <?php _e('أصل', 'auction-system'); ?></span>
                                        </div>
                                    </div>
                                    <div class="card-status">
                                        <?php if ($type->is_active): ?>
                                            <span class="status-badge active">
                                                <span class="dashicons dashicons-yes-alt"></span>
                                                <?php _e('نشط', 'auction-system'); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="status-badge inactive">
                                                <span class="dashicons dashicons-dismiss"></span>
                                                <?php _e('غير نشط', 'auction-system'); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="card-content">
                                    <?php if (!empty($type->description)): ?>
                                        <p class="description"><?php echo esc_html($type->description); ?></p>
                                    <?php else: ?>
                                        <p class="no-description"><?php _e('لا يوجد وصف', 'auction-system'); ?></p>
                                    <?php endif; ?>
                                </div>

                                <div class="card-actions">
                                    <button type="button" class="action-btn edit-btn edit-asset-type"
                                            data-id="<?php echo $type->id; ?>"
                                            data-name="<?php echo esc_attr($type->name); ?>"
                                            data-description="<?php echo esc_attr($type->description ?? ''); ?>"
                                            data-sort-order="<?php echo $type->sort_order; ?>"
                                            data-active="<?php echo $type->is_active; ?>">
                                        <span class="dashicons dashicons-edit"></span>
                                        <?php _e('تحرير', 'auction-system'); ?>
                                    </button>

                                    <button type="button" class="action-btn toggle-btn toggle-asset-type"
                                            data-id="<?php echo $type->id; ?>"
                                            data-active="<?php echo $type->is_active; ?>">
                                        <?php if ($type->is_active): ?>
                                            <span class="dashicons dashicons-hidden"></span>
                                            <?php _e('إلغاء تفعيل', 'auction-system'); ?>
                                        <?php else: ?>
                                            <span class="dashicons dashicons-visibility"></span>
                                            <?php _e('تفعيل', 'auction-system'); ?>
                                        <?php endif; ?>
                                    </button>

                                    <?php if ($this->get_usage_count($type->id) == 0): ?>
                                        <button type="button" class="action-btn delete-btn delete-asset-type"
                                                data-id="<?php echo $type->id; ?>">
                                            <span class="dashicons dashicons-trash"></span>
                                            <?php _e('حذف', 'auction-system'); ?>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <div class="empty-icon">
                            <span class="dashicons dashicons-category"></span>
                        </div>
                        <h3><?php _e('لا توجد أنواع أصول', 'auction-system'); ?></h3>
                        <p><?php _e('ابدأ بإضافة أول نوع أصل لتنظيم مزاداتك', 'auction-system'); ?></p>
                        <button type="button" class="button button-primary add-new-asset-type">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php _e('إضافة نوع جديد', 'auction-system'); ?>
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Edit Modal -->
        <div id="edit-asset-type-modal" class="asset-type-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><?php _e('تحرير نوع الأصل', 'auction-system'); ?></h3>
                    <button type="button" class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="edit-asset-type-form">
                        <input type="hidden" id="edit_asset_type_id" name="id">

                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="edit_asset_type_name"><?php _e('اسم النوع', 'auction-system'); ?> *</label>
                                </th>
                                <td>
                                    <input type="text" id="edit_asset_type_name" name="name" class="regular-text" required>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="edit_asset_type_description"><?php _e('الوصف', 'auction-system'); ?></label>
                                </th>
                                <td>
                                    <textarea id="edit_asset_type_description" name="description" class="large-text" rows="3"></textarea>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="edit_asset_type_sort_order"><?php _e('ترتيب العرض', 'auction-system'); ?></label>
                                </th>
                                <td>
                                    <input type="number" id="edit_asset_type_sort_order" name="sort_order" class="small-text" min="0">
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="edit_asset_type_active"><?php _e('الحالة', 'auction-system'); ?></label>
                                </th>
                                <td>
                                    <label>
                                        <input type="checkbox" id="edit_asset_type_active" name="is_active" value="1">
                                        <?php _e('نشط', 'auction-system'); ?>
                                    </label>
                                </td>
                            </tr>
                        </table>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="button button-secondary modal-close"><?php _e('إلغاء', 'auction-system'); ?></button>
                    <button type="button" class="button button-primary save-asset-type"><?php _e('حفظ التغييرات', 'auction-system'); ?></button>
                </div>
            </div>
        </div>

        <style>
        /* Main Container */
        .auction-asset-types-page {
            background: #f8f9fa;
            min-height: 100vh;
            padding: 0;
        }

        /* Page Header */
        .auction-page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 0;
            margin: 0 -20px 25px -20px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
        }

        .auction-page-header .wrap {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .auction-page-title h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .auction-page-title h1 .dashicons {
            font-size: 2.5rem;
            width: 2.5rem;
            height: 2.5rem;
        }

        .auction-page-description {
            margin: 10px 0 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .auction-page-actions .button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .auction-page-actions .button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* Statistics Overview */
        .auction-stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e1e8ed;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .stat-card.active {
            border-left: 4px solid #27ae60;
        }

        .stat-card.inactive {
            border-left: 4px solid #e74c3c;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #6c757d;
        }

        .stat-card.active .stat-icon {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
        }

        .stat-card.inactive .stat-icon {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .stat-icon .dashicons {
            font-size: 24px;
            width: 24px;
            height: 24px;
        }

        .stat-content h3 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
        }

        .stat-content p {
            margin: 5px 0 0 0;
            color: #7f8c8d;
            font-weight: 500;
        }

        /* Form Container */
        .auction-form-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e1e8ed;
            margin-bottom: 30px;
            overflow: hidden;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .auction-form-header {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px 30px;
            border-bottom: 1px solid #e1e8ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .auction-form-header h2 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.4rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .close-form {
            background: none;
            border: none;
            color: #6c757d;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .close-form:hover {
            background: #e9ecef;
            color: #495057;
        }

        .auction-form {
            padding: 30px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.95rem;
        }

        .required {
            color: #e74c3c;
            margin-right: 3px;
        }

        .form-input,
        .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #fff;
            font-family: inherit;
        }

        .form-input:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-help {
            display: block;
            margin-top: 5px;
            color: #7f8c8d;
            font-size: 0.85rem;
        }

        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-checkbox input[type="checkbox"] {
            display: none;
        }

        .checkmark {
            width: 20px;
            height: 20px;
            border: 2px solid #e1e8ed;
            border-radius: 4px;
            position: relative;
            transition: all 0.3s ease;
        }

        .form-checkbox input[type="checkbox"]:checked + .checkmark {
            background: #3498db;
            border-color: #3498db;
        }

        .form-checkbox input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 12px;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e1e8ed;
        }

        .form-actions .button {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-actions .button-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border: none;
            color: white;
        }

        .form-actions .button-primary:hover {
            background: linear-gradient(135deg, #2980b9, #1f5f8b);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        /* Content Container */
        .auction-content-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e1e8ed;
            overflow: hidden;
        }

        .auction-content-header {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px 30px;
            border-bottom: 1px solid #e1e8ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .auction-content-header h2 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.4rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .items-count {
            background: #3498db;
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        /* Asset Types Grid */
        .asset-types-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            padding: 30px;
        }

        .asset-type-card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e1e8ed;
            transition: all 0.3s ease;
            overflow: hidden;
            position: relative;
        }

        .asset-type-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            border-color: #3498db;
        }

        .card-header {
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-bottom: 1px solid #e1e8ed;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .card-title h3 {
            margin: 0 0 8px 0;
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .card-meta {
            display: flex;
            gap: 15px;
            font-size: 0.85rem;
            color: #7f8c8d;
        }

        .card-meta span {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 5px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-badge.active {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-badge.inactive {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-badge .dashicons {
            font-size: 14px;
            width: 14px;
            height: 14px;
        }

        .card-content {
            padding: 20px;
            min-height: 60px;
        }

        .card-content .description {
            margin: 0;
            color: #34495e;
            line-height: 1.6;
        }

        .card-content .no-description {
            margin: 0;
            color: #bdc3c7;
            font-style: italic;
        }

        .card-actions {
            padding: 15px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e1e8ed;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            text-decoration: none;
        }

        .action-btn .dashicons {
            font-size: 14px;
            width: 14px;
            height: 14px;
        }

        .edit-btn {
            background: #3498db;
            color: white;
        }

        .edit-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .toggle-btn {
            background: #6c757d;
            color: white;
        }

        .toggle-btn:hover {
            background: #5a6268;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        .delete-btn {
            background: #e74c3c;
            color: white;
        }

        .delete-btn:hover {
            background: #c0392b;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 80px 40px;
            color: #7f8c8d;
        }

        .empty-icon {
            margin-bottom: 20px;
        }

        .empty-icon .dashicons {
            font-size: 64px;
            width: 64px;
            height: 64px;
            color: #bdc3c7;
        }

        .empty-state h3 {
            margin: 0 0 10px 0;
            font-size: 1.5rem;
            color: #2c3e50;
        }

        .empty-state p {
            margin: 0 0 30px 0;
            font-size: 1.1rem;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Edit Modal */
        .asset-type-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 100000;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(5px);
        }

        .asset-type-modal .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 90%;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .asset-type-modal .modal-header {
            padding: 25px 30px;
            border-bottom: 1px solid #e1e8ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }

        .asset-type-modal .modal-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .asset-type-modal .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6c757d;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .asset-type-modal .modal-close:hover {
            background: #e9ecef;
            color: #495057;
        }

        .asset-type-modal .modal-body {
            padding: 30px;
        }

        .asset-type-modal .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid #e1e8ed;
            background: #f8f9fa;
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        .asset-type-modal .modal-footer .button {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .auction-page-header {
                padding: 30px 0;
                margin: 0 -10px 20px -10px;
            }

            .auction-page-header .wrap {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .auction-page-title h1 {
                font-size: 2rem;
            }

            .auction-stats-overview {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .stat-card {
                padding: 20px;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .asset-types-grid {
                grid-template-columns: 1fr;
                padding: 20px;
                gap: 20px;
            }

            .card-actions {
                flex-direction: column;
                gap: 8px;
            }

            .action-btn {
                justify-content: center;
            }

            .asset-type-modal .modal-content {
                width: 95%;
                margin: 20px;
            }

            .asset-type-modal .modal-header,
            .asset-type-modal .modal-body,
            .asset-type-modal .modal-footer {
                padding: 20px;
            }
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            // Show/Hide add form
            $('.add-new-asset-type').on('click', function() {
                $('#add-asset-type-form').slideToggle(300);
            });

            // Close form
            $('.close-form').on('click', function() {
                $('#add-asset-type-form').slideUp(300);
            });

            // Edit asset type
            $('.edit-asset-type').on('click', function() {
                var $button = $(this);
                var id = $button.data('id');
                var name = $button.data('name');
                var description = $button.data('description');
                var sortOrder = $button.data('sort-order');
                var isActive = $button.data('active');

                $('#edit_asset_type_id').val(id);
                $('#edit_asset_type_name').val(name);
                $('#edit_asset_type_description').val(description);
                $('#edit_asset_type_sort_order').val(sortOrder);
                $('#edit_asset_type_active').prop('checked', isActive == 1);

                $('#edit-asset-type-modal').fadeIn(300);
            });

            // Close modal
            $('.modal-close').on('click', function() {
                $('#edit-asset-type-modal').fadeOut(300);
            });

            // Close modal on backdrop click
            $('.asset-type-modal').on('click', function(e) {
                if (e.target === this) {
                    $(this).fadeOut(300);
                }
            });

            // Save asset type
            $('.save-asset-type').on('click', function() {
                var $button = $(this);
                var originalText = $button.text();

                $button.prop('disabled', true).html('<span class="dashicons dashicons-update-alt" style="animation: spin 1s linear infinite;"></span> جاري الحفظ...');

                var formData = $('#edit-asset-type-form').serialize();

                $.post(ajaxurl, {
                    action: 'edit_asset_type',
                    nonce: '<?php echo wp_create_nonce('asset_type_ajax'); ?>',
                    data: formData
                }, function(response) {
                    if (response.success) {
                        // Show success message
                        $button.html('<span class="dashicons dashicons-yes-alt"></span> تم الحفظ!').css('background', '#27ae60');
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        alert(response.data);
                        $button.prop('disabled', false).text(originalText);
                    }
                }).fail(function() {
                    alert('حدث خطأ في الاتصال');
                    $button.prop('disabled', false).text(originalText);
                });
            });

            // Toggle asset type
            $('.toggle-asset-type').on('click', function() {
                var $button = $(this);
                var id = $button.data('id');
                var isActive = $button.data('active');
                var originalText = $button.html();

                $button.prop('disabled', true).html('<span class="dashicons dashicons-update-alt" style="animation: spin 1s linear infinite;"></span> جاري التحديث...');

                $.post(ajaxurl, {
                    action: 'toggle_asset_type',
                    id: id,
                    nonce: '<?php echo wp_create_nonce('asset_type_ajax'); ?>'
                }, function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.data);
                        $button.prop('disabled', false).html(originalText);
                    }
                }).fail(function() {
                    alert('حدث خطأ في الاتصال');
                    $button.prop('disabled', false).html(originalText);
                });
            });

            // Delete asset type
            $('.delete-asset-type').on('click', function() {
                var $button = $(this);
                var id = $button.data('id');

                if (confirm('<?php _e('هل أنت متأكد من حذف هذا النوع؟ هذا الإجراء لا يمكن التراجع عنه.', 'auction-system'); ?>')) {
                    var originalText = $button.html();
                    $button.prop('disabled', true).html('<span class="dashicons dashicons-update-alt" style="animation: spin 1s linear infinite;"></span> جاري الحذف...');

                    $.post(ajaxurl, {
                        action: 'delete_asset_type',
                        id: id,
                        nonce: '<?php echo wp_create_nonce('asset_type_ajax'); ?>'
                    }, function(response) {
                        if (response.success) {
                            // Animate card removal
                            $button.closest('.asset-type-card').fadeOut(300, function() {
                                $(this).remove();
                                // Update count
                                var currentCount = parseInt($('.items-count').text());
                                $('.items-count').text((currentCount - 1) + ' <?php _e('نوع', 'auction-system'); ?>');
                            });
                        } else {
                            alert(response.data);
                            $button.prop('disabled', false).html(originalText);
                        }
                    }).fail(function() {
                        alert('حدث خطأ في الاتصال');
                        $button.prop('disabled', false).html(originalText);
                    });
                }
            });

            // Add spinning animation for loading states
            $('<style>').text(`
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `).appendTo('head');
        });
        </script>
        <?php
    }

    /**
     * Get asset types
     */
    private function get_asset_types() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'auction_asset_types';

        // Check if description column exists, if not add it
        $this->ensure_description_column();

        return $wpdb->get_results("
            SELECT * FROM $table_name
            ORDER BY sort_order ASC, name ASC
        ");
    }

    /**
     * Ensure description column exists
     */
    private function ensure_description_column() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'auction_asset_types';

        // Check if description column exists
        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_name LIKE 'description'");

        if (empty($column_exists)) {
            // Add description column
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN description text AFTER slug");
        }
    }

    /**
     * Get usage count for asset type
     */
    private function get_usage_count($type_id) {
        global $wpdb;

        $assets_table = $wpdb->prefix . 'auction_assets';

        return intval($wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $assets_table WHERE asset_type_id = %d",
            $type_id
        )));
    }

    /**
     * Handle form submission
     */
    private function handle_form_submission() {
        if (!isset($_POST['asset_type_nonce']) || !wp_verify_nonce($_POST['asset_type_nonce'], 'asset_type_action')) {
            return;
        }

        if (!current_user_can('manage_options')) {
            return;
        }

        $action = $_POST['action'];

        if ($action === 'add') {
            $this->add_asset_type_form();
        }
    }

    /**
     * Add asset type from form
     */
    private function add_asset_type_form() {
        global $wpdb;

        $name = sanitize_text_field($_POST['name']);
        $description = sanitize_textarea_field($_POST['description'] ?? '');
        $sort_order = intval($_POST['sort_order'] ?? 0);
        $is_active = isset($_POST['is_active']) ? 1 : 0;

        if (empty($name)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('اسم النوع مطلوب', 'auction-system') . '</p></div>';
            });
            return;
        }

        // Generate slug
        $slug = sanitize_title($name);

        // Check if slug exists
        $table_name = $wpdb->prefix . 'auction_asset_types';
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE slug = %s",
            $slug
        ));

        if ($existing) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('يوجد نوع أصل بنفس الاسم', 'auction-system') . '</p></div>';
            });
            return;
        }

        // Insert new asset type
        $result = $wpdb->insert(
            $table_name,
            array(
                'name' => $name,
                'slug' => $slug,
                'description' => $description,
                'sort_order' => $sort_order,
                'is_active' => $is_active
            ),
            array('%s', '%s', '%s', '%d', '%d')
        );

        if ($result) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('تم إضافة نوع الأصل بنجاح', 'auction-system') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('فشل في إضافة نوع الأصل', 'auction-system') . '</p></div>';
            });
        }
    }

    /**
     * Add asset type via AJAX
     */
    public function add_asset_type() {
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'asset_type_ajax')) {
            wp_send_json_error(__('خطأ في التحقق من الأمان', 'auction-system'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('ليس لديك صلاحية لهذا الإجراء', 'auction-system'));
        }

        // This method can be used for AJAX additions if needed
        wp_send_json_success(__('تم إضافة نوع الأصل بنجاح', 'auction-system'));
    }

    /**
     * Edit asset type via AJAX
     */
    public function edit_asset_type() {
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'asset_type_ajax')) {
            wp_send_json_error(__('خطأ في التحقق من الأمان', 'auction-system'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('ليس لديك صلاحية لهذا الإجراء', 'auction-system'));
        }

        global $wpdb;

        // Parse form data
        parse_str($_POST['data'], $form_data);

        $id = intval($form_data['id']);
        $name = sanitize_text_field($form_data['name']);
        $description = sanitize_textarea_field($form_data['description'] ?? '');
        $sort_order = intval($form_data['sort_order'] ?? 0);
        $is_active = isset($form_data['is_active']) ? 1 : 0;

        if (empty($name)) {
            wp_send_json_error(__('اسم النوع مطلوب', 'auction-system'));
        }

        $table_name = $wpdb->prefix . 'auction_asset_types';

        $result = $wpdb->update(
            $table_name,
            array(
                'name' => $name,
                'description' => $description,
                'sort_order' => $sort_order,
                'is_active' => $is_active
            ),
            array('id' => $id),
            array('%s', '%s', '%d', '%d'),
            array('%d')
        );

        if ($result !== false) {
            wp_send_json_success(__('تم تحديث نوع الأصل بنجاح', 'auction-system'));
        } else {
            wp_send_json_error(__('فشل في تحديث نوع الأصل', 'auction-system'));
        }
    }

    /**
     * Delete asset type via AJAX
     */
    public function delete_asset_type() {
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'asset_type_ajax')) {
            wp_send_json_error(__('خطأ في التحقق من الأمان', 'auction-system'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('ليس لديك صلاحية لهذا الإجراء', 'auction-system'));
        }

        global $wpdb;

        $id = intval($_POST['id']);

        // Check if asset type is being used
        if ($this->get_usage_count($id) > 0) {
            wp_send_json_error(__('لا يمكن حذف نوع الأصل لأنه مستخدم في أصول موجودة', 'auction-system'));
        }

        $table_name = $wpdb->prefix . 'auction_asset_types';

        $result = $wpdb->delete(
            $table_name,
            array('id' => $id),
            array('%d')
        );

        if ($result) {
            wp_send_json_success(__('تم حذف نوع الأصل بنجاح', 'auction-system'));
        } else {
            wp_send_json_error(__('فشل في حذف نوع الأصل', 'auction-system'));
        }
    }

    /**
     * Toggle asset type status via AJAX
     */
    public function toggle_asset_type() {
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'asset_type_ajax')) {
            wp_send_json_error(__('خطأ في التحقق من الأمان', 'auction-system'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('ليس لديك صلاحية لهذا الإجراء', 'auction-system'));
        }

        global $wpdb;

        $id = intval($_POST['id']);
        $table_name = $wpdb->prefix . 'auction_asset_types';

        // Get current status
        $current_status = $wpdb->get_var($wpdb->prepare(
            "SELECT is_active FROM $table_name WHERE id = %d",
            $id
        ));

        $new_status = $current_status ? 0 : 1;

        $result = $wpdb->update(
            $table_name,
            array('is_active' => $new_status),
            array('id' => $id),
            array('%d'),
            array('%d')
        );

        if ($result !== false) {
            $message = $new_status ? __('تم تفعيل نوع الأصل', 'auction-system') : __('تم إلغاء تفعيل نوع الأصل', 'auction-system');
            wp_send_json_success($message);
        } else {
            wp_send_json_error(__('فشل في تغيير حالة نوع الأصل', 'auction-system'));
        }
    }
}
