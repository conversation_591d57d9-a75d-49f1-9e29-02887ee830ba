<?php
/**
 * Test AJAX Handlers for Auction System
 * 
 * This file can be used to test if the AJAX handlers are properly registered
 * Place this file in the theme root and access it via browser to test
 */

// Include WordPress
require_once('../../../wp-load.php');

// Check if user is logged in and has proper capabilities
if (!is_user_logged_in()) {
    die('You must be logged in to test this.');
}

if (!current_user_can('edit_posts')) {
    die('You do not have sufficient permissions.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Auction AJAX Handlers</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Auction AJAX Handlers</h1>
    
    <div>
        <h2>Test Approve Auction</h2>
        <input type="number" id="approve-auction-id" placeholder="Auction ID" value="1">
        <button id="test-approve">Test Approve</button>
        <div id="approve-result"></div>
    </div>
    
    <div>
        <h2>Test Reject Auction</h2>
        <input type="number" id="reject-auction-id" placeholder="Auction ID" value="1">
        <button id="test-reject">Test Reject</button>
        <div id="reject-result"></div>
    </div>
    
    <div>
        <h2>Test Delete Auction</h2>
        <input type="number" id="delete-auction-id" placeholder="Auction ID" value="1">
        <button id="test-delete">Test Delete</button>
        <div id="delete-result"></div>
    </div>

    <script>
    var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
    
    jQuery(document).ready(function($) {
        // Test approve
        $('#test-approve').on('click', function() {
            var auctionId = $('#approve-auction-id').val();
            console.log('Testing approve for auction ID:', auctionId);
            
            $.post(ajaxurl, {
                action: 'approve_auction',
                auction_id: auctionId,
                nonce: '<?php echo wp_create_nonce('auction_action'); ?>'
            }, function(response) {
                console.log('Approve response:', response);
                $('#approve-result').html('<pre>' + JSON.stringify(response, null, 2) + '</pre>');
            }).fail(function(xhr, status, error) {
                console.error('AJAX Error:', xhr.responseText);
                $('#approve-result').html('<pre>Error: ' + xhr.responseText + '</pre>');
            });
        });
        
        // Test reject
        $('#test-reject').on('click', function() {
            var auctionId = $('#reject-auction-id').val();
            console.log('Testing reject for auction ID:', auctionId);
            
            $.post(ajaxurl, {
                action: 'reject_auction',
                auction_id: auctionId,
                nonce: '<?php echo wp_create_nonce('auction_action'); ?>'
            }, function(response) {
                console.log('Reject response:', response);
                $('#reject-result').html('<pre>' + JSON.stringify(response, null, 2) + '</pre>');
            }).fail(function(xhr, status, error) {
                console.error('AJAX Error:', xhr.responseText);
                $('#reject-result').html('<pre>Error: ' + xhr.responseText + '</pre>');
            });
        });
        
        // Test delete
        $('#test-delete').on('click', function() {
            var auctionId = $('#delete-auction-id').val();
            console.log('Testing delete for auction ID:', auctionId);
            
            $.post(ajaxurl, {
                action: 'delete_auction',
                auction_id: auctionId,
                nonce: '<?php echo wp_create_nonce('auction_action'); ?>'
            }, function(response) {
                console.log('Delete response:', response);
                $('#delete-result').html('<pre>' + JSON.stringify(response, null, 2) + '</pre>');
            }).fail(function(xhr, status, error) {
                console.error('AJAX Error:', xhr.responseText);
                $('#delete-result').html('<pre>Error: ' + xhr.responseText + '</pre>');
            });
        });
    });
    </script>
</body>
</html>
