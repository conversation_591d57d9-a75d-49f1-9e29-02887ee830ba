/**
 * Single Auction Page - Notifications & Toasts
 *
 * @package AuctionSystem
 * @version 1.0.0
 * @description إدارة الإشعارات والرسائل المنبثقة
 */

(function($) {
    'use strict';

    /**
     * Show toast notification
     */
    function showToast(message, type = 'success') {
        const toast = $(`
            <div class="toast-notification" style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#27ae60' : '#e74c3c'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            ">
                ${message}
            </div>
        `);

        $('body').append(toast);

        // Animate in
        setTimeout(() => {
            toast.css('transform', 'translateX(0)');
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            toast.css('transform', 'translateX(100%)');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, 3000);
    }

    // Make functions globally available
    window.AuctionNotifications = {
        showToast: showToast
    };



})(jQuery);
