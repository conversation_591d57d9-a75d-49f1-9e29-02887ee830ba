<?php
/**
 * Auction Admin Columns
 * 
 * @package AuctionSystem
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Auction Admin Columns Class
 */
class AuctionAdminColumns {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_filter('manage_auction_posts_columns', array($this, 'add_custom_columns'));
        add_action('manage_auction_posts_custom_column', array($this, 'display_custom_columns'), 10, 2);
        add_filter('manage_edit-auction_sortable_columns', array($this, 'make_columns_sortable'));
        add_action('pre_get_posts', array($this, 'handle_column_sorting'));
        add_filter('post_row_actions', array($this, 'modify_row_actions'), 10, 2);
        add_action('admin_head', array($this, 'add_admin_styles'));
    }
    
    /**
     * Add custom columns
     */
    public function add_custom_columns($columns) {
        // Remove default columns we don't need
        unset($columns['date']);
        unset($columns['author']);
        
        // Add our custom columns
        $new_columns = array();
        $new_columns['cb'] = $columns['cb'];
        $new_columns['title'] = $columns['title'];
        $new_columns['company'] = __('الشركة المنظمة', 'auction-system');
        $new_columns['auction_date'] = __('تاريخ المزاد', 'auction-system');
        $new_columns['auction_city'] = __('المدينة', 'auction-system');
        $new_columns['auction_type'] = __('نوع المزاد', 'auction-system');
        $new_columns['assets_count'] = __('عدد الأصول', 'auction-system');
        $new_columns['status'] = __('الحالة', 'auction-system');
        $new_columns['created'] = __('تاريخ الإنشاء', 'auction-system');
        
        return $new_columns;
    }
    
    /**
     * Display custom column content
     */
    public function display_custom_columns($column, $post_id) {
        switch ($column) {
            case 'company':
                $company_name = get_post_meta($post_id, '_company_name', true);
                echo esc_html($company_name);
                break;
                
            case 'auction_date':
                $auction_date = get_post_meta($post_id, '_auction_date', true);
                $auction_time = get_post_meta($post_id, '_auction_time', true);
                
                if ($auction_date) {
                    echo '<strong>' . date_i18n('j F Y', strtotime($auction_date)) . '</strong>';
                    if ($auction_time) {
                        echo '<br><small>' . date_i18n('g:i A', strtotime($auction_time)) . '</small>';
                    }
                    
                    // Show countdown for upcoming auctions
                    $auction_datetime = strtotime($auction_date . ' ' . $auction_time);
                    $current_time = current_time('timestamp');
                    
                    if ($auction_datetime > $current_time) {
                        $time_diff = $auction_datetime - $current_time;
                        $days = floor($time_diff / 86400);
                        
                        if ($days > 0) {
                            echo '<br><small style="color: #0073aa;">' . sprintf(__('خلال %d يوم', 'auction-system'), $days) . '</small>';
                        } else {
                            echo '<br><small style="color: #d63638;">' . __('اليوم', 'auction-system') . '</small>';
                        }
                    }
                } else {
                    echo '<span style="color: #999;">' . __('غير محدد', 'auction-system') . '</span>';
                }
                break;
                
            case 'auction_city':
                $city = get_post_meta($post_id, '_auction_city', true);
                echo esc_html($city);
                break;
                
            case 'auction_type':
                $terms = wp_get_post_terms($post_id, 'auction_type');
                if (!empty($terms) && !is_wp_error($terms)) {
                    $type_names = array();
                    foreach ($terms as $term) {
                        $type_names[] = $term->name;
                    }
                    echo esc_html(implode(', ', $type_names));
                } else {
                    echo '<span style="color: #999;">' . __('غير محدد', 'auction-system') . '</span>';
                }
                break;
                
            case 'assets_count':
                global $wpdb;
                $assets_table = $wpdb->prefix . 'auction_assets';
                $count = $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM $assets_table WHERE auction_id = %d",
                    $post_id
                ));
                
                if ($count > 0) {
                    echo '<strong>' . number_format_i18n($count) . '</strong>';
                } else {
                    echo '<span style="color: #d63638;">0</span>';
                }
                break;
                
            case 'status':
                $post_status = get_post_status($post_id);
                echo $this->get_status_badge($post_status);
                break;
                
            case 'created':
                $post = get_post($post_id);
                echo date_i18n('j F Y', strtotime($post->post_date));
                echo '<br><small>' . date_i18n('g:i A', strtotime($post->post_date)) . '</small>';
                break;
        }
    }
    
    /**
     * Make columns sortable
     */
    public function make_columns_sortable($columns) {
        $columns['company'] = 'company';
        $columns['auction_date'] = 'auction_date';
        $columns['auction_city'] = 'auction_city';
        $columns['assets_count'] = 'assets_count';
        $columns['created'] = 'date';
        
        return $columns;
    }
    
    /**
     * Handle column sorting
     */
    public function handle_column_sorting($query) {
        if (!is_admin() || !$query->is_main_query()) {
            return;
        }
        
        if ($query->get('post_type') !== 'auction') {
            return;
        }
        
        $orderby = $query->get('orderby');
        
        switch ($orderby) {
            case 'company':
                $query->set('meta_key', '_company_name');
                $query->set('orderby', 'meta_value');
                break;
                
            case 'auction_date':
                $query->set('meta_key', '_auction_date');
                $query->set('orderby', 'meta_value');
                break;
                
            case 'auction_city':
                $query->set('meta_key', '_auction_city');
                $query->set('orderby', 'meta_value');
                break;
                
            case 'assets_count':
                // This requires a more complex query, so we'll handle it differently
                add_filter('posts_join', array($this, 'assets_count_join'));
                add_filter('posts_groupby', array($this, 'assets_count_groupby'));
                add_filter('posts_orderby', array($this, 'assets_count_orderby'));
                break;
        }
    }
    
    /**
     * Join for assets count sorting
     */
    public function assets_count_join($join) {
        global $wpdb;
        $assets_table = $wpdb->prefix . 'auction_assets';
        $join .= " LEFT JOIN $assets_table ON $wpdb->posts.ID = $assets_table.auction_id";
        return $join;
    }
    
    /**
     * Group by for assets count sorting
     */
    public function assets_count_groupby($groupby) {
        global $wpdb;
        $groupby = "$wpdb->posts.ID";
        return $groupby;
    }
    
    /**
     * Order by for assets count sorting
     */
    public function assets_count_orderby($orderby) {
        global $wpdb;
        $order = (isset($_GET['order']) && $_GET['order'] === 'desc') ? 'DESC' : 'ASC';
        $orderby = "COUNT($wpdb->prefix" . "auction_assets.id) $order";
        return $orderby;
    }
    
    /**
     * Get status badge
     */
    private function get_status_badge($status) {
        $badges = array(
            'pending' => array(
                'class' => 'auction-status-pending',
                'text' => __('في انتظار المراجعة', 'auction-system')
            ),
            'publish' => array(
                'class' => 'auction-status-publish',
                'text' => __('معتمد', 'auction-system')
            ),
            'draft' => array(
                'class' => 'auction-status-draft',
                'text' => __('مسودة', 'auction-system')
            ),
            'private' => array(
                'class' => 'auction-status-private',
                'text' => __('خاص', 'auction-system')
            )
        );
        
        $badge = $badges[$status] ?? array('class' => 'auction-status-draft', 'text' => $status);
        
        return sprintf(
            '<span class="auction-status-badge %s">%s</span>',
            $badge['class'],
            $badge['text']
        );
    }
    
    /**
     * Modify row actions
     */
    public function modify_row_actions($actions, $post) {
        if ($post->post_type !== 'auction') {
            return $actions;
        }
        
        // Add quick actions for pending auctions
        if ($post->post_status === 'pending') {
            $actions['approve'] = sprintf(
                '<a href="#" class="auction-quick-approve" data-id="%d">%s</a>',
                $post->ID,
                __('اعتماد سريع', 'auction-system')
            );
            
            $actions['reject'] = sprintf(
                '<a href="#" class="auction-quick-reject" data-id="%d" style="color: #d63638;">%s</a>',
                $post->ID,
                __('رفض', 'auction-system')
            );
        }
        
        // Add view assets action
        $actions['view_assets'] = sprintf(
            '<a href="%s">%s</a>',
            admin_url('post.php?post=' . $post->ID . '&action=edit#auction-assets'),
            __('عرض الأصول', 'auction-system')
        );
        
        return $actions;
    }
    
    /**
     * Add admin styles
     */
    public function add_admin_styles() {
        $screen = get_current_screen();
        if ($screen && $screen->post_type === 'auction') {
            ?>
            <style>
            .auction-status-badge {
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                display: inline-block;
            }
            
            .auction-status-pending {
                background: #fff3cd;
                color: #856404;
                border: 1px solid #ffeaa7;
            }
            
            .auction-status-publish {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            
            .auction-status-draft {
                background: #e2e3e5;
                color: #383d41;
                border: 1px solid #d1ecf1;
            }
            
            .auction-status-private {
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            
            .column-company,
            .column-auction_city {
                width: 15%;
            }
            
            .column-auction_date {
                width: 12%;
            }
            
            .column-auction_type,
            .column-assets_count {
                width: 10%;
            }
            
            .column-status {
                width: 12%;
            }
            
            .column-created {
                width: 10%;
            }
            
            .auction-quick-approve,
            .auction-quick-reject {
                font-weight: 600;
            }
            
            .auction-quick-approve:hover {
                color: #00a32a;
            }
            
            .auction-quick-reject:hover {
                color: #d63638;
            }
            </style>
            
            <script>
            jQuery(document).ready(function($) {
                // Quick approve
                $('.auction-quick-approve').on('click', function(e) {
                    e.preventDefault();
                    var postId = $(this).data('id');
                    var $row = $(this).closest('tr');
                    
                    if (confirm('<?php _e('هل تريد اعتماد هذا المزاد؟', 'auction-system'); ?>')) {
                        $.post(ajaxurl, {
                            action: 'approve_auction',
                            auction_id: postId,
                            nonce: '<?php echo wp_create_nonce('auction_action'); ?>'
                        }, function(response) {
                            if (response.success) {
                                $row.find('.auction-status-badge').removeClass('auction-status-pending').addClass('auction-status-publish').text('<?php _e('معتمد', 'auction-system'); ?>');
                                $row.find('.auction-quick-approve, .auction-quick-reject').remove();
                            } else {
                                alert(response.data);
                            }
                        });
                    }
                });
                
                // Quick reject
                $('.auction-quick-reject').on('click', function(e) {
                    e.preventDefault();
                    var postId = $(this).data('id');
                    var $row = $(this).closest('tr');
                    
                    if (confirm('<?php _e('هل تريد رفض هذا المزاد؟', 'auction-system'); ?>')) {
                        $.post(ajaxurl, {
                            action: 'reject_auction',
                            auction_id: postId,
                            nonce: '<?php echo wp_create_nonce('auction_action'); ?>'
                        }, function(response) {
                            if (response.success) {
                                $row.find('.auction-status-badge').removeClass('auction-status-pending').addClass('auction-status-draft').text('<?php _e('مسودة', 'auction-system'); ?>');
                                $row.find('.auction-quick-approve, .auction-quick-reject').remove();
                            } else {
                                alert(response.data);
                            }
                        });
                    }
                });
            });
            </script>
            <?php
        }
    }
}

// Initialize admin columns
if (is_admin()) {
    new AuctionAdminColumns();
}
