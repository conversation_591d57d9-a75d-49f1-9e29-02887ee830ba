/**
 * Asset Filters Styles
 *
 * @package AuctionSystem
 */

.asset-filters-container {
    background-color: #f8f9fa; /* Light background for the filter section */
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    border: 1px solid #e0e0e0;
}

.asset-filters-form .filter-fields-grid {
    display: grid;
    /* Attempt to fit more items per row by reducing minmax or using more fixed columns for wider screens */
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); /* Reduced min width */
    gap: 15px; /* Reduced gap */
    margin-bottom: 20px;
    align-items: end; /* Align items to the bottom of their grid cell, useful if labels make heights uneven */
}

/* If you want a specific field (like area slider) to span more columns */
.asset-filters-form .filter-field-large {
    grid-column: span 2; /* Example: make it span 2 columns */
}
@media (max-width: 992px) { /* Adjust breakpoint as needed */
    .asset-filters-form .filter-field-large {
        grid-column: span 1; /* Revert to 1 column on smaller screens */
    }
}

.asset-filters-form .filter-field {
    display: flex;
    flex-direction: column;
}

.asset-filters-form .filter-field label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
    font-size: 0.9rem;
}

.asset-filters-form .filter-input,
.asset-filters-form .filter-select {
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    width: 100%;
    box-sizing: border-box; /* Ensures padding doesn't affect width */
}

.asset-filters-form .filter-input:focus,
.asset-filters-form .filter-select:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.asset-filters-form .area-range-slider-container {
    padding-top: 10px;
}

.asset-filters-form #area-range-slider {
    /* Styles for the range slider itself (e.g., from noUiSlider) will go here or be handled by its library */
    margin-bottom: 10px;
}

.asset-filters-form .area-range-values {
    font-size: 0.9rem;
    color: #555;
    text-align: center;
}

.asset-filters-form .filter-actions {
    display: flex;
    gap: 10px; /* Reduced gap for buttons */
    justify-content: flex-start;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
    margin-top: 15px;
}

.asset-filters-form .filter-button {
    padding: 8px 15px; /* Smaller padding */
    font-size: 0.9rem; /* Smaller font size */
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.asset-filters-form .filter-button i {
    margin-right: 8px; /* Space between icon and text, RTL aware */
}

/* Specific styling for reset button if needed */
.asset-filters-form .reset-button {
    background-color: #6c757d;
    color: white;
    border-color: #6c757d;
}

.asset-filters-form .reset-button:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .asset-filters-form .filter-fields-grid {
        grid-template-columns: 1fr; /* Stack fields on smaller screens */
    }

    .asset-filters-form .filter-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .asset-filters-form .filter-button {
        width: 100%;
        margin-bottom: 10px;
    }
    .asset-filters-form .filter-button:last-child {
        margin-bottom: 0;
    }
}

/* --- noUiSlider Customizations --- */
/* Styles for noUiSlider removed to revert to its default. Specific feedback needed to re-style. */

/* --- Button Style Refinements --- */
.asset-filters-form .filter-button {
    /* padding: 8px 15px; */ /* Already set */
    /* font-size: 0.9rem; */ /* Already set */
    border-radius: 20px; /* More rounded buttons */
    border: 1px solid transparent;
    transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out, transform 0.1s ease-out;
    font-weight: 500;
}

.asset-filters-form .filter-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
.asset-filters-form .filter-button:active {
    transform: translateY(0px);
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}


/* Primary Search Button */
.asset-filters-form .btn-primary.filter-button {
    background-color: #dc3545; /* Theme's Red (example, replace with actual theme red) */
    border-color: #dc3545;
    color: white;
}
.asset-filters-form .btn-primary.filter-button:hover {
    background-color: #c82333; /* Darker red for hover */
    border-color: #bd2130;
}

/* Reset Button */
.asset-filters-form .reset-button {
    background-color: #f8f9fa; /* Lighter background */
    color: #495057; /* Darker text */
    border: 1px solid #ced4da; /* Standard border */
}

.asset-filters-form .reset-button:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #343a40;
}