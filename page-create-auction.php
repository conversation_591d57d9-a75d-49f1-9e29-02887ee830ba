<?php
/**
 * Template Name: Create Auction
 *
 * @package AuctionSystem
 * @version 1.0.0
 */

get_header();

// Enqueue required scripts and styles FIRST
wp_enqueue_script('auction-create-form', get_stylesheet_directory_uri() . '/assets/auction-script.js', array('jquery'), AUCTION_VERSION, true);
wp_enqueue_style('auction-create-form', get_stylesheet_directory_uri() . '/assets/auction-style.css', array(), AUCTION_VERSION);

// Check if user is logged in (if guest posting is disabled)
$auction_settings = AuctionSettings::get_instance();
$allow_guest_posting = $auction_settings->get_setting('allow_guest_posting', '0');

if (!is_user_logged_in() && $allow_guest_posting !== '1') {
    ?>
    <div class="auction-login-required">
        <div class="container">
            <div class="login-message">
                <h2><?php _e('تسجيل الدخول مطلوب', 'auction-system'); ?></h2>
                <p><?php _e('يجب تسجيل الدخول لإنشاء مزاد جديد', 'auction-system'); ?></p>
                <a href="<?php echo wp_login_url(get_permalink()); ?>" class="btn btn-primary">
                    <?php _e('تسجيل الدخول', 'auction-system'); ?>
                </a>
            </div>
        </div>
    </div>
    <?php
    get_footer();
    return;
}

// Include required files
require_once get_stylesheet_directory() . '/inc/auction-frontend.php';

// Initialize frontend handler
$auction_frontend = new AuctionFrontend();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $result = $auction_frontend->handle_form_submission();

    if ($result['success']) {
        // Show success message in place of form
        ?>
        <div class="auction-create-page">
            <div class="container">
                <div class="auction-form-container">
                    <div class="auction-success-message">
                        <div class="success-content">
                            <div class="success-icon">
                                <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                    <polyline points="22,4 12,14.01 9,11.01"></polyline>
                                </svg>
                            </div>
                            <h2><?php _e('تم إرسال المزاد بنجاح!', 'auction-system'); ?></h2>
                            <p><?php _e('تم إرسال المزاد للمراجعة من قبل الإدارة. سيتم إشعارك عند الموافقة عليه وسيتم نشره على الموقع.', 'auction-system'); ?></p>

                            <div class="success-details">
                                <div class="detail-item">
                                    <span class="detail-icon">📋</span>
                                    <span class="detail-text"><?php _e('المزاد تحت المراجعة', 'auction-system'); ?></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-icon">⏰</span>
                                    <span class="detail-text"><?php _e('سيتم الرد خلال 24-48 ساعة', 'auction-system'); ?></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-icon">📧</span>
                                    <span class="detail-text"><?php _e('سيتم إشعارك عبر البريد الإلكتروني', 'auction-system'); ?></span>
                                </div>
                            </div>

                            <div class="success-actions">
                                <a href="<?php echo home_url(); ?>" class="btn btn-primary">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                        <polyline points="9,22 9,12 15,12 15,22"></polyline>
                                    </svg>
                                    <?php _e('العودة للرئيسية', 'auction-system'); ?>
                                </a>
                                <a href="<?php echo get_permalink(); ?>" class="btn btn-secondary">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <line x1="12" y1="5" x2="12" y2="19"></line>
                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                    </svg>
                                    <?php _e('إنشاء مزاد آخر', 'auction-system'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
        get_footer();
        return;
    } else {
        // Show error message
        $error_message = $result['message'];
    }
}
?>

<div class="auction-create-page">
    <div class="container">
        <div class="page-header">
            <h1><?php _e('إنشاء مزاد جديد', 'auction-system'); ?></h1>
            <p class="page-description">
                <?php _e('املأ النموذج التالي لإنشاء مزاد جديد. جميع الحقول المطلوبة مميزة بعلامة *', 'auction-system'); ?>
            </p>
        </div>

        <?php if (isset($error_message)): ?>
            <div class="auction-error-message">
                <div class="error-content">
                    <strong><?php _e('خطأ:', 'auction-system'); ?></strong>
                    <?php echo esc_html($error_message); ?>
                </div>
            </div>
        <?php endif; ?>

        <div class="auction-form-container">
            <!-- Progress Steps -->
            <div class="form-progress">
                <div class="progress-steps">
                    <div class="step active" data-step="1">
                        <div class="step-number">1</div>
                        <div class="step-title"><?php _e('معلومات المزاد', 'auction-system'); ?></div>
                    </div>
                    <div class="step" data-step="2">
                        <div class="step-number">2</div>
                        <div class="step-title"><?php _e('معلومات الشركة', 'auction-system'); ?></div>
                    </div>
                    <div class="step" data-step="3">
                        <div class="step-number">3</div>
                        <div class="step-title"><?php _e('الأصول', 'auction-system'); ?></div>
                    </div>
                    <div class="step" data-step="4">
                        <div class="step-number">4</div>
                        <div class="step-title"><?php _e('البروشور', 'auction-system'); ?></div>
                    </div>
                    <div class="step" data-step="5">
                        <div class="step-number">5</div>
                        <div class="step-title"><?php _e('مراجعة وحفظ', 'auction-system'); ?></div>
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 20%;"></div>
                </div>
            </div>

            <!-- Form -->
            <form id="auction-form" method="post" enctype="multipart/form-data" novalidate>
                <?php wp_nonce_field('create_auction_nonce', 'create_auction_nonce'); ?>

                <!-- Step 1: Auction Details -->
                <div class="form-step active" data-step="1">
                    <div class="step-content">
                        <h3><?php _e('معلومات المزاد الأساسية', 'auction-system'); ?></h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="auction_title" class="required">
                                    <?php _e('اسم المزاد', 'auction-system'); ?> *
                                </label>
                                <input type="text" id="auction_title" name="auction_title"
                                       class="form-control" required
                                       placeholder="<?php _e('مثال: مزاد نقوة الرياض', 'auction-system'); ?>">
                                <div class="error-message"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="auction_description">
                                    <?php _e('وصف المزاد', 'auction-system'); ?>
                                </label>
                                <textarea id="auction_description" name="auction_description"
                                          class="form-control" rows="4"
                                          placeholder="<?php _e('وصف مختصر عن المزاد...', 'auction-system'); ?>"></textarea>
                                <div class="error-message"></div>
                            </div>
                        </div>

                        <div class="form-row two-cols">
                            <div class="form-group">
                                <label for="auction_date" class="required">
                                    <?php _e('تاريخ المزاد', 'auction-system'); ?> *
                                </label>
                                <input type="date" id="auction_date" name="auction_date"
                                       class="form-control" required
                                       min="<?php echo date('Y-m-d'); ?>">
                                <div class="error-message"></div>
                            </div>
                            <div class="form-group">
                                <label for="auction_time" class="required">
                                    <?php _e('وقت المزاد', 'auction-system'); ?> *
                                </label>
                                <input type="time" id="auction_time" name="auction_time"
                                       class="form-control" required>
                                <div class="error-message"></div>
                            </div>
                        </div>

                        <div class="form-row two-cols">
                            <div class="form-group">
                                <label for="auction_city" class="required">
                                    <?php _e('المدينة الرئيسية', 'auction-system'); ?> *
                                </label>
                                <input type="text" id="auction_city" name="auction_city"
                                       class="form-control" required
                                       placeholder="<?php _e('الرياض', 'auction-system'); ?>">
                                <div class="error-message"></div>
                            </div>
                            <div class="form-group">
                                <label for="auction_type" class="required">
                                    <?php _e('نوع المزاد', 'auction-system'); ?> *
                                </label>
                                <select id="auction_type" name="auction_type" class="form-control" required>
                                    <option value=""><?php _e('اختر نوع المزاد', 'auction-system'); ?></option>
                                    <option value="in-person"><?php _e('حضوري', 'auction-system'); ?></option>
                                    <option value="online"><?php _e('إلكتروني', 'auction-system'); ?></option>
                                    <option value="hybrid"><?php _e('هجين', 'auction-system'); ?></option>
                                </select>
                                <div class="error-message"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="auction_location">
                                    <?php _e('موقع المزاد على Google Maps', 'auction-system'); ?>
                                </label>
                                <input type="url" id="auction_location" name="auction_location"
                                       class="form-control"
                                       placeholder="https://maps.google.com/...">
                                <div class="error-message"></div>
                                <p class="description" style="margin-top: 8px; font-size: 14px; color: #666;">
                                    <?php _e('انسخ رابط الموقع من Google Maps لعرض موقع المزاد على الخريطة', 'auction-system'); ?>
                                </p>
                                <div class="map-preview" id="auction-map-preview" style="display: none;">
                                    <!-- Map will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-navigation">
                        <button type="button" class="btn btn-primary next-step">
                            <?php _e('التالي', 'auction-system'); ?>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"></polyline>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Step 2: Company Details -->
                <div class="form-step" data-step="2">
                    <div class="step-content">
                        <h3><?php _e('معلومات الشركة المنظمة', 'auction-system'); ?></h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="company_name" class="required">
                                    <?php _e('اسم الشركة', 'auction-system'); ?> *
                                </label>
                                <input type="text" id="company_name" name="company_name"
                                       class="form-control" required
                                       placeholder="<?php _e('اسم الشركة المنظمة للمزاد', 'auction-system'); ?>">
                                <div class="error-message"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="company_license" class="required">
                                    <?php _e('رقم الترخيص', 'auction-system'); ?> *
                                </label>
                                <input type="text" id="company_license" name="company_license"
                                       class="form-control" required
                                       placeholder="<?php _e('رقم ترخيص الشركة', 'auction-system'); ?>">
                                <div class="error-message"></div>
                            </div>
                        </div>

                        <div class="form-row two-cols">
                            <div class="form-group">
                                <label for="company_phone" class="required">
                                    <?php _e('رقم الهاتف', 'auction-system'); ?> *
                                </label>
                                <input type="tel" id="company_phone" name="company_phone"
                                       class="form-control" required
                                       placeholder="<?php _e('+966501234567', 'auction-system'); ?>">
                                <div class="error-message"></div>
                            </div>
                            <div class="form-group">
                                <label for="company_email" class="required">
                                    <?php _e('البريد الإلكتروني', 'auction-system'); ?> *
                                </label>
                                <input type="email" id="company_email" name="company_email"
                                       class="form-control" required
                                       placeholder="<?php _e('<EMAIL>', 'auction-system'); ?>">
                                <div class="error-message"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="company_address" class="required">
                                    <?php _e('العنوان', 'auction-system'); ?> *
                                </label>
                                <textarea id="company_address" name="company_address"
                                          class="form-control" rows="2" required
                                          placeholder="<?php _e('العنوان الكامل للشركة...', 'auction-system'); ?>"></textarea>
                                <div class="error-message"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="company_website">
                                    <?php _e('الموقع الإلكتروني', 'auction-system'); ?>
                                </label>
                                <input type="url" id="company_website" name="company_website"
                                       class="form-control"
                                       placeholder="https://example.com">
                                <div class="error-message"></div>
                            </div>
                        </div>
                    </div>

                    <div class="form-navigation">
                        <button type="button" class="btn btn-secondary prev-step">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="15,18 9,12 15,6"></polyline>
                            </svg>
                            <?php _e('السابق', 'auction-system'); ?>
                        </button>
                        <button type="button" class="btn btn-primary next-step">
                            <?php _e('التالي', 'auction-system'); ?>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"></polyline>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Step 3: Assets -->
                <div class="form-step" data-step="3">
                    <div class="step-content">
                        <h3><?php _e('إضافة الأصول', 'auction-system'); ?></h3>

                        <div id="assets-container">
                            <div class="assets-empty" style="text-align: center; padding: 40px;">
                                <p><?php _e('لم يتم إضافة أي أصول بعد', 'auction-system'); ?></p>
                                <button type="button" class="btn btn-primary add-first-asset">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <line x1="12" y1="5" x2="12" y2="19"></line>
                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                    </svg>
                                    <?php _e('إضافة أصل جديد', 'auction-system'); ?>
                                </button>
                            </div>

                            <div class="assets-list" style="display: none;"></div>

                            <div class="add-asset-section" style="display: none; text-align: center; margin-top: 20px;">
                                <button type="button" class="btn btn-secondary add-asset">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <line x1="12" y1="5" x2="12" y2="19"></line>
                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                    </svg>
                                    <?php _e('إضافة أصل آخر', 'auction-system'); ?>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="form-navigation">
                        <button type="button" class="btn btn-secondary prev-step">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="15,18 9,12 15,6"></polyline>
                            </svg>
                            <?php _e('السابق', 'auction-system'); ?>
                        </button>
                        <button type="button" class="btn btn-primary next-step">
                            <?php _e('التالي', 'auction-system'); ?>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"></polyline>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Step 4: Brochure -->
                <div class="form-step" data-step="4">
                    <div class="step-content">
                        <h3><?php _e('رفع البروشور', 'auction-system'); ?></h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="brochure" class="required">
                                    <?php _e('ملف البروشور (PDF)', 'auction-system'); ?> *
                                </label>
                                <div class="brochure-upload-area" id="brochure-upload-area">
                                    <div class="upload-icon">
                                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"></path>
                                        </svg>
                                    </div>
                                    <div class="upload-text"><?php _e('اضغط لاختيار ملف البروشور أو اسحبه هنا', 'auction-system'); ?></div>
                                    <div class="upload-hint">
                                        <?php
                                        $max_size = $auction_settings->get_setting('max_brochure_size_mb', '10');
                                        printf(__('ملف PDF فقط، الحد الأقصى %s ميجابايت', 'auction-system'), $max_size);
                                        ?>
                                    </div>
                                </div>

                                <input type="file" id="brochure" name="brochure_temp" class="brochure-upload-input" accept=".pdf" style="display: none;" required>

                                <!-- Brochure preview area -->
                                <div class="brochure-preview" id="brochure-preview" style="display: none;">
                                    <!-- Preview content will be added here -->
                                </div>

                                <!-- Hidden input to store brochure data -->
                                <input type="hidden" id="brochure-data" name="brochure_data" value="">
                                <input type="hidden" id="brochure-name" name="brochure_name" value="">
                                <input type="hidden" id="brochure-size" name="brochure_size" value="">

                                <div class="error-message"></div>
                                <p class="description" style="margin-top: 15px;">
                                    <?php _e('البروشور يجب أن يحتوي على تفاصيل المزاد والشروط والأحكام وجميع المعلومات المطلوبة للمشاركين.', 'auction-system'); ?>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="form-navigation">
                        <button type="button" class="btn btn-secondary prev-step">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="15,18 9,12 15,6"></polyline>
                            </svg>
                            <?php _e('السابق', 'auction-system'); ?>
                        </button>
                        <button type="button" class="btn btn-primary next-step">
                            <?php _e('التالي', 'auction-system'); ?>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"></polyline>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Step 5: Review and Submit -->
                <div class="form-step" data-step="5">
                    <div class="step-content">
                        <h3><?php _e('مراجعة وإرسال المزاد', 'auction-system'); ?></h3>

                        <div class="review-section">
                            <div class="review-card">
                                <h4><?php _e('معلومات المزاد', 'auction-system'); ?></h4>
                                <div class="review-content" id="review-auction-details"></div>
                            </div>

                            <div class="review-card">
                                <h4><?php _e('معلومات الشركة', 'auction-system'); ?></h4>
                                <div class="review-content" id="review-company-details"></div>
                            </div>

                            <div class="review-card">
                                <h4><?php _e('الأصول', 'auction-system'); ?></h4>
                                <div class="review-content" id="review-assets"></div>
                            </div>

                            <div class="review-card">
                                <h4><?php _e('البروشور', 'auction-system'); ?></h4>
                                <div class="review-content" id="review-brochure"></div>
                            </div>
                        </div>

                        <div class="terms-section" style="margin-top: 30px;">
                            <label class="checkbox-container">
                                <input type="checkbox" id="accept_terms" name="accept_terms" required>
                                <span class="checkmark"></span>
                                <?php _e('أوافق على الشروط والأحكام وأؤكد صحة جميع البيانات المدخلة', 'auction-system'); ?> *
                            </label>
                            <div class="error-message"></div>
                        </div>
                    </div>

                    <div class="form-navigation">
                        <button type="button" class="btn btn-secondary prev-step">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="15,18 9,12 15,6"></polyline>
                            </svg>
                            <?php _e('السابق', 'auction-system'); ?>
                        </button>

                        <div class="submit-buttons">
                            <?php if (is_user_logged_in()): ?>
                                <button type="submit" name="save_as_draft" class="btn btn-secondary">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                                        <polyline points="17,21 17,13 7,13 7,21"></polyline>
                                        <polyline points="7,3 7,8 15,8"></polyline>
                                    </svg>
                                    <?php _e('حفظ كمسودة', 'auction-system'); ?>
                                </button>
                            <?php endif; ?>

                            <button type="submit" name="submit_for_review" class="btn btn-success">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="20,6 9,17 4,12"></polyline>
                                </svg>
                                <?php _e('إرسال للمراجعة', 'auction-system'); ?>
                            </button>
                        </div>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>

<?php
// Localize script
wp_localize_script('auction-create-form', 'auctionAjax', array(
    'ajaxurl' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('auction_ajax_nonce'),
    'maxImages' => $auction_settings->get_setting('max_images_per_asset', '10'),
    'maxImageSize' => $auction_settings->get_setting('max_image_size_mb', '5'),
    'maxBrochureSize' => $auction_settings->get_setting('max_brochure_size_mb', '10'),
    'strings' => array(
        'required_field' => __('هذا الحقل مطلوب', 'auction-system'),
        'invalid_email' => __('البريد الإلكتروني غير صحيح', 'auction-system'),
        'invalid_url' => __('الرابط غير صحيح', 'auction-system'),
        'file_too_large' => __('حجم الملف كبير جداً', 'auction-system'),
        'invalid_file_type' => __('نوع الملف غير مدعوم', 'auction-system'),
        'loading' => __('جاري التحميل...', 'auction-system'),
        'error' => __('حدث خطأ', 'auction-system')
    )
));

get_footer();
?>
