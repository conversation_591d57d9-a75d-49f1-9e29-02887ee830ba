# نظام المزادات العقارية - WordPress Theme

نظام شامل لإدارة المزادات العقارية مبني على WordPress مع دعم كامل للغة العربية.

## 🚀 الميزات الرئيسية

### 📝 إنشاء وإدارة المزادات
- نموذج متعدد الخطوات لإنشاء المزادات
- تحقق شامل من صحة البيانات
- دعم التعديل والحفظ كمسودة
- نظام مراجعة وموافقة من الإدارة

### 🏠 إدارة الأصول
- إضافة أصول متعددة لكل مزاد
- رفع صور متعددة لكل أصل
- دعم أنواع مختلفة من الأصول (سكني، تجاري، أراضي، مركبات)
- تحقق من وجود أصل واحد على الأقل

### 🗺️ تكامل Google Maps
- إضافة موقع المزاد على الخريطة
- إضافة موقع كل أصل منفرد
- عرض تفاعلي للخرائط
- دعم الإحداثيات وأسماء الأماكن

### 📄 إدارة البروشورات
- رفع ملفات PDF للبروشورات
- معاينة وإدارة الملفات
- تحديد حد أقصى لحجم الملفات

### 🎨 تصميم متجاوب
- تصميم متجاوب مع جميع الأجهزة
- واجهة مستخدم عربية أنيقة
- تأثيرات بصرية متقدمة
- دعم كامل للـ RTL

## 📁 هيكل المشروع

```
generatepress_child/
├── assets/
│   ├── auction-script.js          # JavaScript الرئيسي للنماذج
│   ├── auction-style.css          # تنسيقات النماذج
│   ├── auction-single.css         # تنسيقات صفحة المزاد المفرد
│   ├── auction-single.js          # JavaScript صفحة المزاد المفرد
│   ├── edit-auction/              # ملفات التعديل
│   ├── images/                    # الصور الافتراضية
│   └── single-auction/            # ملفات صفحة المزاد المفرد
├── inc/
│   ├── auction-init.php           # تهيئة النظام
│   ├── auction-frontend.php       # معالجة النماذج
│   ├── auction-form-renderer.php  # عرض النماذج
│   ├── auction-database.php       # قاعدة البيانات
│   ├── auction-settings.php       # الإعدادات
│   └── edit-auction/              # نظام التعديل
├── page-create-auction.php        # صفحة إنشاء المزاد
├── page-edit-auction.php          # صفحة تعديل المزاد
├── single-auction.php             # صفحة المزاد المفرد
└── functions.php                  # الدوال الرئيسية
```

## 🛠️ التثبيت

1. **رفع الملفات:**
   ```bash
   # رفع مجلد الثيم إلى
   wp-content/themes/generatepress_child/
   ```

2. **تفعيل الثيم:**
   - اذهب إلى لوحة التحكم > المظهر > القوالب
   - فعل قالب "GeneratePress Child"

3. **إعداد قاعدة البيانات:**
   - سيتم إنشاء الجداول تلقائياً عند التفعيل

## ⚙️ الإعدادات

### إعدادات النظام
- اذهب إلى: **المزادات > الإعدادات**
- قم بتكوين:
  - حد أقصى لعدد الصور لكل أصل
  - حد أقصى لحجم الصور
  - حد أقصى لحجم البروشورات
  - إعدادات الإعلانات

### الصفحات المطلوبة
- أنشئ صفحة جديدة واختر قالب "إنشاء مزاد"
- أنشئ صفحة جديدة واختر قالب "تعديل مزاد"

## 📊 قاعدة البيانات

### الجداول المستخدمة
- `wp_posts` - بيانات المزادات الأساسية
- `wp_postmeta` - معلومات المزادات التفصيلية
- `wp_auction_assets` - الأصول
- `wp_auction_asset_images` - صور الأصول
- `wp_auction_asset_types` - أنواع الأصول
- `wp_auction_dynamic_options` - الخيارات الديناميكية

## 🔧 التطوير

### إضافة ميزات جديدة
```php
// إضافة نوع أصل جديد
add_action('init', function() {
    // كود إضافة النوع
});
```

### تخصيص التصميم
```css
/* تخصيص ألوان النظام */
:root {
    --primary-color: #your-color;
    --secondary-color: #your-color;
}
```

## 📝 الاستخدام

### إنشاء مزاد جديد
1. اذهب إلى صفحة "إنشاء مزاد"
2. املأ معلومات المزاد (الخطوة 1)
3. أدخل معلومات الشركة (الخطوة 2)
4. أضف الأصول (الخطوة 3) - مطلوب أصل واحد على الأقل
5. ارفع البروشور (الخطوة 4)
6. راجع البيانات وأرسل للمراجعة (الخطوة 5)

### إدارة المزادات
- **لوحة التحكم:** المزادات > جميع المزادات
- **الموافقة:** تغيير حالة المزاد إلى "منشور"
- **التعديل:** رابط "تعديل" في قائمة المزادات

## 🐛 استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في رفع الصور:**
   - تحقق من أذونات مجلد `wp-content/uploads`
   - تحقق من إعدادات PHP لحجم الملفات

2. **مشاكل في الخرائط:**
   - تأكد من صحة روابط Google Maps
   - تحقق من اتصال الإنترنت

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في المستودع
- مراجعة الوثائق
- فحص ملفات السجل في `auction-logs/`

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 🙏 شكر وتقدير

- WordPress Community
- GeneratePress Theme
- Google Maps API
- جميع المساهمين في المشروع

---

**تم التطوير بواسطة:** فريق تطوير نظام المزادات  
**الإصدار:** 1.0.0  
**تاريخ آخر تحديث:** ديسمبر 2024
