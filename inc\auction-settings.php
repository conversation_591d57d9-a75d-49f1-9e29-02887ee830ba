<?php
/**
 * Auction Settings Management
 *
 * @package AuctionSystem
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Auction Settings Class
 */
class AuctionSettings {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }

    /**
     * Initialize
     */
    private function init() {
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_post_save_auction_settings', array($this, 'save_settings'));
        add_action('admin_post_nopriv_save_auction_settings', array($this, 'save_settings'));
    }

    /**
     * Register settings
     */
    public function register_settings() {
        // Settings will be handled manually through custom table
    }

    /**
     * Render settings page
     */
    public function render_page() {
    // Enqueue required styles
    wp_enqueue_style(
        'auction-settings-style',
        get_stylesheet_directory_uri() . '/assets/backend/settings/css/settings.css',
        array(),
        AUCTION_VERSION
    );


        // Enqueue required scripts
        wp_enqueue_script(
            'auction-settings-script',
            get_stylesheet_directory_uri() . '/assets/backend/settings/js/settings.js',
            array('jquery', 'jquery-ui-tooltip'),
            AUCTION_VERSION,
            true
        );

        // Enqueue WordPress media uploader
        wp_enqueue_media();

        // Check for settings update
        if (isset($_GET['settings-updated']) && $_GET['settings-updated'] == 'true') {
            add_settings_error(
                'auction_settings',
                'settings_updated',
                __('تم حفظ الإعدادات بنجاح.', 'auction-system'),
                'updated'
            );
        }

        // Show settings errors/notices
        settings_errors('auction_settings');

        // Get all settings
        $settings = $this->get_all_settings();

        // تحميل القالب الرئيسي والذي سيقوم بدوره بتحميل باقي الأقسام
        require_once get_stylesheet_directory() . '/inc/backend/settings/general-template.php';

        // إضافة متغيرات JavaScript
        wp_localize_script('auction-settings-script', 'auctionSettings', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('auction_settings_nonce'),
            'translations' => array(
                'saveSuccess' => __('تم حفظ الإعدادات بنجاح', 'auction-system'),
                'saveFailed' => __('حدث خطأ أثناء حفظ الإعدادات', 'auction-system'),
                'confirmReset' => __('هل أنت متأكد من إعادة تعيين الإعدادات؟', 'auction-system'),
                'fieldRequired' => __('هذا الحقل مطلوب', 'auction-system'),
            )
        ));
    }

    /**
     * Get all settings
     */
    public function get_all_settings() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'auction_settings';
        $results = $wpdb->get_results("SELECT setting_name, setting_value FROM $table_name", ARRAY_A);

        $settings = array();
        foreach ($results as $result) {
            $settings[$result['setting_name']] = $result['setting_value'];
        }

        // Default values if not set
        $defaults = [
            'max_images_per_asset' => '10',
            'max_image_size_mb' => '5',
            'max_brochure_size_mb' => '10',
            'max_assets_per_auction' => '0',
            'auto_delete_days' => '7',
            'allow_guest_posting' => '0',
            'enable_starting_price' => '0',
            'enable_minimum_bid' => '0',
            'enable_email_notifications' => '0',
            // Lazy Loading Settings
            'lazy_load_enabled' => '1',
            'lazy_load_per_page' => '3',
            'lazy_load_auto_load' => '1',
            'lazy_load_scroll_distance' => '300',
            'lazy_load_animation' => 'fade',

            // New Ads Settings Defaults
            'new_ads_enabled' => '0',
        ];

        $ad_positions_for_defaults = ['top_banner', 'sidebar_top', 'sidebar_bottom', 'content_middle', 'bottom_banner'];
        foreach ($ad_positions_for_defaults as $pos) {
            $defaults["new_ads_{$pos}_enabled"] = '0';
            $defaults["new_ads_{$pos}_type"] = 'html';
            $defaults["new_ads_{$pos}_content"] = ''; // Unified content field
            $defaults["new_ads_{$pos}_image_link_url"] = ''; // For image click-through
            $defaults["new_ads_{$pos}_image_alt"] = '';    // For image alt text
        }

        return array_merge($defaults, $settings);
    }

    /**
     * Get single setting
     */
    public function get_setting($setting_name, $default = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . 'auction_settings';
        $value = $wpdb->get_var($wpdb->prepare(
            "SELECT setting_value FROM $table_name WHERE setting_name = %s",
            $setting_name
        ));

        return $value !== null ? $value : $default;
    }

    /**
     * Update setting
     */
    public function update_setting($setting_name, $setting_value) {
        // Validate lazy loading settings
        if ($setting_name === 'lazy_load_enabled' && $setting_value === '0') {
            // إذا تم تعطيل التحميل التدريجي، قم بتعطيل التحميل التلقائي أيضاً
            $this->update_setting_in_db('lazy_load_auto_load', '0');
        } else if ($setting_name === 'lazy_load_auto_load' && $setting_value === '1') {
            // تأكد من أن التحميل التدريجي مفعل قبل تفعيل التحميل التلقائي
            $lazy_load_enabled = $this->get_setting('lazy_load_enabled', '0');
            if ($lazy_load_enabled !== '1') {
                return false;
            }
        }

        return $this->update_setting_in_db($setting_name, $setting_value);
    }

    /**
     * Update setting in database
     */
    private function update_setting_in_db($setting_name, $setting_value) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'auction_settings';

        // Check if the setting_name already exists
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE setting_name = %s",
            $setting_name
        ));

        if ($count > 0) {
            // Setting exists, so update it
            $result = $wpdb->update(
                $table_name,
                array('setting_value' => $setting_value),
                array('setting_name' => $setting_name),
                array('%s'), // format for value
                array('%s')  // format for where
            );
            
        } else {
            // Setting does not exist, so insert it
            $result = $wpdb->insert(
                $table_name,
                array(
                    'setting_name' => $setting_name,
                    'setting_value' => $setting_value
                ),
                array('%s', '%s') // format for each value
            );
        }
    }

    /**
     * Display advertisement by position
     */
    public function display_ad($position) {
        // Use new setting keys
        $global_enabled_key = 'new_ads_enabled';
        $position_enabled_key = "new_ads_{$position}_enabled";
        $type_key = "new_ads_{$position}_type";
        $content_key = "new_ads_{$position}_content"; // Unified content (HTML or Image URL)
        $image_link_url_key = "new_ads_{$position}_image_link_url";
        $image_alt_key = "new_ads_{$position}_image_alt";

        // Check if ads are enabled globally
        if ($this->get_setting($global_enabled_key, '0') !== '1') {
            return '';
        }

        // Check if specific ad position is enabled
        if ($this->get_setting($position_enabled_key, '0') !== '1') {
            return '';
        }

        // Get ad type and main content
        $ad_type = $this->get_setting($type_key, 'html');
        $main_content = $this->get_setting($content_key, '');

        if (empty($main_content)) {
            return '';
        }

        // Generate HTML based on ad type
        $ad_class = 'auction-ad auction-ad-' . esc_attr($position) . ' auction-ad-' . esc_attr($ad_type);
        $ad_html = '';

        switch ($ad_type) {
            case 'image':
                // $main_content is the image URL
                $link_url = $this->get_setting($image_link_url_key, '#');
                $alt_text = $this->get_setting($image_alt_key, __('Advertisement', 'auction-system'));
                
                $href_attr = !empty($link_url) && $link_url !== '#' ? esc_url($link_url) : '#';
                $target_attr = !empty($link_url) && $link_url !== '#' ? 'target="_blank" rel="noopener"' : '';
                $alt_attr_val = !empty($alt_text) ? esc_attr($alt_text) : __('Advertisement', 'auction-system');

                $ad_html = sprintf(
                    '<a href="%s" %s><img src="%s" alt="%s" style="max-width: 100%%; height: auto; display: block;"></a>',
                    $href_attr,
                    $target_attr,
                    esc_url($main_content), // Image URL
                    $alt_attr_val
                );
                break;

            case 'google_ads': // Assuming 'html' type covers Google Ads as well
            case 'html':
            default:
                // $main_content is the HTML code
                $ad_html = wp_kses_post($main_content);
                break;
        }

        return !empty($ad_html) ? '<div class="' . $ad_class . '">' . $ad_html . '</div>' : '';
    }

    /**
     * Save settings
     */
    public function save_settings() {
        
        // Check nonce
        if (!isset($_POST['auction_settings_nonce']) || !wp_verify_nonce($_POST['auction_settings_nonce'], 'auction_settings_nonce')) {
            wp_die('خطأ في التحقق من الأمان');
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die('ليس لديك صلاحية لتعديل هذه الإعدادات');
        }

        $settings = array(
            'max_images_per_asset',
            'max_image_size_mb',
            'max_brochure_size_mb',
            'max_assets_per_auction',
            'auto_delete_days',
            'lazy_load_per_page',
            'lazy_load_scroll_distance'
        );

        foreach ($settings as $setting) {
            if (isset($_POST[$setting])) {
                $this->update_setting($setting, intval($_POST[$setting]));
            }
        }

        // Handle select fields
        $select_fields = array(
            'lazy_load_animation'
        );

        foreach ($select_fields as $field) {
            if (isset($_POST[$field])) {
                $this->update_setting($field, sanitize_text_field($_POST[$field]));
            }
        }

        $checkboxes = array(
            'allow_guest_posting',
            'enable_starting_price',
            'enable_minimum_bid',
            'enable_email_notifications',
            'lazy_load_enabled',
            'lazy_load_auto_load',
            // New Ads Checkboxes
            'new_ads_enabled', // Global ads toggle
        );

        // Define ad positions to iterate through for saving
        $ad_positions_for_save = ['top_banner', 'sidebar_top', 'sidebar_bottom', 'content_middle', 'bottom_banner'];

        foreach ($ad_positions_for_save as $pos) {
            $checkboxes[] = "new_ads_{$pos}_enabled"; // Add enable toggle for each position
        }
        
        foreach ($checkboxes as $checkbox) {
            $value = isset($_POST[$checkbox]) ? '1' : '0';
            $this->update_setting($checkbox, $value);
        }

        // New Ads Settings Save Logic
        foreach ($ad_positions_for_save as $pos_key) {
            $type_field_name = "new_ads_{$pos_key}_type";
            $content_html_field_name = "new_ads_{$pos_key}_content_html"; // From textarea
            $content_image_url_field_name = "new_ads_{$pos_key}_image_url"; // From image url input
            
            $db_content_field = "new_ads_{$pos_key}_content"; // Unified field in DB for actual ad content
            $db_image_link_url_field = "new_ads_{$pos_key}_image_link_url"; // Click-through URL for image
            $db_image_alt_field = "new_ads_{$pos_key}_image_alt";    // Alt text for image

            // Save Ad Type
            if (isset($_POST[$type_field_name])) {
                $ad_type = sanitize_text_field($_POST[$type_field_name]);
                $this->update_setting($type_field_name, $ad_type);

                // Save Ad Content based on type
                if ($ad_type === 'image') {
                    $image_url_to_save = '';
                    if (isset($_POST[$content_image_url_field_name])) {
                        $image_url_to_save = esc_url_raw($_POST[$content_image_url_field_name]);
                    }
                    $this->update_setting($db_content_field, $image_url_to_save);

                    // Save image link URL and alt text
                    $link_url_to_save = '';
                    if (isset($_POST["new_ads_{$pos_key}_link_url"])) {
                        $link_url_to_save = esc_url_raw($_POST["new_ads_{$pos_key}_link_url"]);
                    }
                    $this->update_setting($db_image_link_url_field, $link_url_to_save);
                    
                    $alt_text_to_save = '';
                    if (isset($_POST["new_ads_{$pos_key}_alt"])) {
                        $alt_text_to_save = sanitize_text_field($_POST["new_ads_{$pos_key}_alt"]);
                    }
                    $this->update_setting($db_image_alt_field, $alt_text_to_save);

                } else { // html or google_ads
                    $html_content_to_save = '';
                    if (isset($_POST[$content_html_field_name])) {
                        $html_content_to_save = wp_kses_post($_POST[$content_html_field_name]);
                    }
                    $this->update_setting($db_content_field, $html_content_to_save);
                    
                    // Clear image specific fields if type is not image, as they are not applicable
                    $this->update_setting($db_image_link_url_field, '');
                    $this->update_setting($db_image_alt_field, '');
                }
            } else {
                 // If type is not set (which shouldn't happen if the field is present), clear content and image specific fields
                 $this->update_setting($db_content_field, '');
                 $this->update_setting($db_image_link_url_field, '');
                 $this->update_setting($db_image_alt_field, '');
            }
        }

        $redirect_url = add_query_arg('settings-updated', 'true', wp_get_referer());

        wp_redirect($redirect_url);
        exit;
    }
}
