<?php
/**
 * Auction System Initialization
 *
 * @package AuctionSystem
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Main Auction System Class
 */
class AuctionSystem {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }

    /**
     * Initialize the auction system
     */
    private function init() {
        // Include required files
        $this->include_files();

        // Initialize hooks
        add_action('init', array($this, 'init_hooks'));
        add_action('wp_loaded', array($this, 'init_database'));

        // Admin hooks
        if (is_admin()) {
            add_action('admin_menu', array($this, 'add_admin_menu'));
        }
    }

    /**
     * Include required files
     */
    private function include_files() {
        $files = array(
            'auction-database.php',
            'auction-post-type.php',
            'auction-meta-fields.php',
            'auction-settings.php',
            'auction-frontend.php',
            'auction-admin-dashboard.php',
            'auction-asset-types.php',
            'auction-admin-columns.php',
            'auction-ads-display.php'
        );

        foreach ($files as $file) {
            $file_path = AUCTION_PLUGIN_PATH . $file;
            if (file_exists($file_path)) {
                require_once $file_path;
            }
        }
    }

    /**
     * Initialize hooks
     */
    public function init_hooks() {
        // Register post type
        if (class_exists('AuctionPostType')) {
            AuctionPostType::get_instance();
        }

        // Initialize meta fields
        if (class_exists('AuctionMetaFields')) {
            AuctionMetaFields::get_instance();
        }

        // Initialize settings
        if (class_exists('AuctionSettings')) {
            AuctionSettings::get_instance();
        }

        // Initialize frontend
        if (class_exists('AuctionFrontend')) {
            new AuctionFrontend();
        }

        // Load text domain for translations
        add_action('after_setup_theme', array($this, 'load_textdomain'));
    }

    /**
     * Load text domain
     */
    public function load_textdomain() {
        load_theme_textdomain('auction-system', get_stylesheet_directory() . '/languages');
    }

    /**
     * Initialize database
     */
    public function init_database() {
        if (class_exists('AuctionDatabase')) {
            AuctionDatabase::get_instance()->create_tables();
        }
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('إدارة المزادات', 'auction-system'),
            __('المزادات', 'auction-system'),
            'edit_posts',
            'auction-system',
            array($this, 'admin_page'),
            'dashicons-awards',
            30
        );

        add_submenu_page(
            'auction-system',
            __('إعدادات المزادات', 'auction-system'),
            __('الإعدادات', 'auction-system'),
            'edit_posts',
            'auction-settings',
            array($this, 'settings_page')
        );

        add_submenu_page(
            'auction-system',
            __('أنواع الأصول', 'auction-system'),
            __('أنواع الأصول', 'auction-system'),
            'edit_posts',
            'auction-asset-types',
            array($this, 'asset_types_page')
        );
    }

    /**
     * Admin page callback
     */
    public function admin_page() {
        // Include admin dashboard
        require_once AUCTION_PLUGIN_PATH . 'auction-admin-dashboard.php';
        $dashboard = new AuctionAdminDashboard();
        $dashboard->render();
    }

    /**
     * Settings page callback
     */
    public function settings_page() {
        if (class_exists('AuctionSettings')) {
            AuctionSettings::get_instance()->render_page();
        } else {
            echo '<div class="notice notice-error"><p>خطأ: لم يتم العثور على كلاس AuctionSettings</p></div>';
        }
    }

    /**
     * Asset types page callback
     */
    public function asset_types_page() {
        // Include asset types manager
        require_once AUCTION_PLUGIN_PATH . 'auction-asset-types.php';
        $asset_types_manager = new AuctionAssetTypes();
        $asset_types_manager->render();
    }

}

// Initialize the auction system
AuctionSystem::get_instance();
