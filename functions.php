<?php
/**
 * GeneratePress child theme functions and definitions.
 *
 * Add your custom PHP in this file.
 * Only edit this file if you have direct access to it on your server (to fix errors if they happen).
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Enqueue scripts and styles
function auction_enqueue_scripts() {
    // Always load auction-main.js (needed for countdown and lazy loading)
    wp_enqueue_script(
        'auction-main',
        get_stylesheet_directory_uri() . '/assets/auction-main.js',
        array('jquery'),
        AUCTION_VERSION,
        true
    );
    // Localize script with data (if needed elsewhere, but main settings are passed from PHP components)
    // wp_localize_script('auction-main', 'auctionData', ...);
}
add_action('wp_enqueue_scripts', 'auction_enqueue_scripts');

// Define auction system constants
define('AUCTION_PLUGIN_PATH', get_stylesheet_directory() . '/inc/');
define('AUCTION_PLUGIN_URL', get_stylesheet_directory_uri() . '/inc/');
define('AUCTION_VERSION', '1.0.0');

// Include auction system files
require_once AUCTION_PLUGIN_PATH . 'auction-post-type.php';
require_once AUCTION_PLUGIN_PATH . 'auction-init.php';

// Include edit auction system
require_once AUCTION_PLUGIN_PATH . 'edit-auction/edit-auction-handler.php';

// Include unified form renderer
require_once AUCTION_PLUGIN_PATH . 'auction-form-renderer.php';
require_once get_stylesheet_directory() . '/inc/enqueue-asset-filters.php'; // Enqueue for asset filters
require_once get_stylesheet_directory() . '/inc/ajax-asset-filters-handler.php'; // AJAX handler for asset filters

// Create edit auction page on theme activation
add_action('after_switch_theme', function() {
    // Create edit auction page
    $page = get_page_by_path('edit-auction');

    if (!$page) {
        $page_data = array(
            'post_title' => 'تعديل المزاد',
            'post_content' => '',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => 'edit-auction'
        );

        $page_id = wp_insert_post($page_data);

        if ($page_id && !is_wp_error($page_id)) {
            update_post_meta($page_id, '_wp_page_template', 'page-edit-auction.php');
        }
    }

    // Flush rewrite rules
    flush_rewrite_rules();
});

// New auction system components integrated into existing files

// Force flush rewrite rules on theme activation (one time)
add_action('after_switch_theme', 'auction_flush_rewrite_rules');
function auction_flush_rewrite_rules() {
    // Make sure post types are registered
    if (class_exists('AuctionPostType')) {
        AuctionPostType::get_instance();
    }

    // Flush rewrite rules
    flush_rewrite_rules();
}

/**
 * Display advertisement by position - Helper function
 *
 * @param string $position The ad position (top_banner, sidebar, etc.)
 * @return void
 */
function auction_display_ad($position) {
    if (class_exists('AuctionSettings')) {
        $auction_settings = AuctionSettings::get_instance();
        echo $auction_settings->display_ad($position);
    }
}

/**
 * Check if ads are enabled
 *
 * @return bool
 */
function auction_ads_enabled() {
    if (class_exists('AuctionSettings')) {
        $auction_settings = AuctionSettings::get_instance();
        return $auction_settings->get_setting('ads_enabled') === '1';
    }
    return false;
}

// Enqueue asset filters script
function enqueue_asset_filters_script() {
    if (is_singular('auction')) {
        wp_enqueue_script(
            'asset-filters',
            get_stylesheet_directory_uri() . '/assets/single-auction/js/asset-filters.js',
            array('jquery'),
            filemtime(get_stylesheet_directory() . '/assets/single-auction/js/asset-filters.js'),
            true
        );

        // Localize script with data
        wp_localize_script('asset-filters', 'auctionData', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('asset_filters_nonce'),
            'auction_id' => get_the_ID(),
            'lazy_settings' => array(
                'lazy_load_enabled' => AuctionSettings::get_instance()->get_setting('lazy_load_enabled', '1'),
                'lazy_load_per_page' => AuctionSettings::get_instance()->get_setting('lazy_load_per_page', '3'),
                'lazy_load_auto_load' => AuctionSettings::get_instance()->get_setting('lazy_load_auto_load', '0'), // Force to 0 since it's disabled in DB
                'lazy_load_scroll_distance' => AuctionSettings::get_instance()->get_setting('lazy_load_scroll_distance', '300'),
                'lazy_load_animation' => AuctionSettings::get_instance()->get_setting('lazy_load_animation', 'fade')
            )
        ));
    }
}
add_action('wp_enqueue_scripts', 'enqueue_asset_filters_script');