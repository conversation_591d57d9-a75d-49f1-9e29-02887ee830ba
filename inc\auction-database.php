<?php
/**
 * Auction Database Management
 *
 * @package AuctionSystem
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Auction Database Class
 */
class AuctionDatabase {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        // Constructor logic if needed
    }

    /**
     * Create database tables
     */
    public function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Create auction assets table
        $this->create_assets_table($charset_collate);

        // Create asset types table
        $this->create_asset_types_table($charset_collate);

        // Create settings table
        $this->create_settings_table($charset_collate);

        // Create logs table
        $this->create_logs_table($charset_collate);

        // Insert default data
        $this->insert_default_data();

        // Update version
        update_option('auction_db_version', AUCTION_VERSION);
    }

    /**
     * Create auction assets table
     */
    private function create_assets_table($charset_collate) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'auction_assets';

        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            auction_id bigint(20) NOT NULL,
            asset_type_id bigint(20) NOT NULL,
            title varchar(255) NOT NULL,
            description text,
            area varchar(100),
            location varchar(255),
            city varchar(100),
            district varchar(100),
            map_link text,
            coordinates varchar(100),
            images text,
            deed_number varchar(100),
            starting_price decimal(15,2) DEFAULT NULL,
            minimum_bid decimal(15,2) DEFAULT NULL,
            sort_order int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY auction_id (auction_id),
            KEY asset_type_id (asset_type_id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Create asset types table
     */
    private function create_asset_types_table($charset_collate) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'auction_asset_types';

        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            slug varchar(255) NOT NULL,
            description text,
            sort_order int(11) DEFAULT 0,
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY slug (slug)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Create settings table
     */
    private function create_settings_table($charset_collate) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'auction_settings';

        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            setting_name varchar(255) NOT NULL,
            setting_value longtext,
            autoload varchar(20) NOT NULL DEFAULT 'yes',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY setting_name (setting_name)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Create logs table
     */
    private function create_logs_table($charset_collate) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'auction_logs';

        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            auction_id bigint(20) DEFAULT NULL,
            user_id bigint(20) DEFAULT NULL,
            action varchar(100) NOT NULL,
            details text,
            ip_address varchar(45),
            user_agent text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY auction_id (auction_id),
            KEY user_id (user_id),
            KEY action (action),
            KEY created_at (created_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);


    }



    /**
     * Insert default data
     */
    private function insert_default_data() {
        $this->insert_default_asset_types();
        $this->insert_default_settings();
    }

    /**
     * Insert default asset types
     */
    private function insert_default_asset_types() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'auction_asset_types';

        // Check if data already exists
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        if ($count > 0) {
            return;
        }

        $default_types = array(
            array('name' => 'أراضي سكنية', 'slug' => 'residential-land', 'sort_order' => 1),
            array('name' => 'أراضي تجارية', 'slug' => 'commercial-land', 'sort_order' => 2),
            array('name' => 'أراضي زراعية', 'slug' => 'agricultural-land', 'sort_order' => 3),
            array('name' => 'أراضي صناعية', 'slug' => 'industrial-land', 'sort_order' => 4),
            array('name' => 'فلل', 'slug' => 'villas', 'sort_order' => 5),
            array('name' => 'شقق', 'slug' => 'apartments', 'sort_order' => 6),
            array('name' => 'مباني تجارية', 'slug' => 'commercial-buildings', 'sort_order' => 7),
            array('name' => 'مستودعات', 'slug' => 'warehouses', 'sort_order' => 8),
            array('name' => 'مزارع', 'slug' => 'farms', 'sort_order' => 9)
        );

        foreach ($default_types as $type) {
            $wpdb->insert($table_name, $type);
        }
    }

    /**
     * Insert default settings
     */
    private function insert_default_settings() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'auction_settings';

        // Check if settings already exist
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        if ($count > 0) {
            return;
        }

        $default_settings = array(
            array('setting_name' => 'max_images_per_asset', 'setting_value' => '10'),
            array('setting_name' => 'max_image_size_mb', 'setting_value' => '5'),
            array('setting_name' => 'max_brochure_size_mb', 'setting_value' => '10'),
            array('setting_name' => 'max_assets_per_auction', 'setting_value' => '0'),
            array('setting_name' => 'auto_delete_days', 'setting_value' => '7'),
            array('setting_name' => 'allow_guest_posting', 'setting_value' => '0'),
            array('setting_name' => 'enable_starting_price', 'setting_value' => '0'),
            array('setting_name' => 'enable_minimum_bid', 'setting_value' => '0'),
            array('setting_name' => 'enable_email_notifications', 'setting_value' => '0'),
            // Lazy Loading Settings
            array('setting_name' => 'lazy_load_enabled', 'setting_value' => '1'),
            array('setting_name' => 'lazy_load_per_page', 'setting_value' => '3'),
            array('setting_name' => 'lazy_load_auto_load', 'setting_value' => '1'),
            array('setting_name' => 'lazy_load_scroll_distance', 'setting_value' => '300'),
            array('setting_name' => 'lazy_load_animation', 'setting_value' => 'fade'),
            // Ads Settings - Simple System with Types
            array('setting_name' => 'ads_enabled', 'setting_value' => '1'),

            // Top Banner Ad
            array('setting_name' => 'ads_top_banner_enabled', 'setting_value' => '1'),
            array('setting_name' => 'ads_top_banner_type', 'setting_value' => 'google_ads'),
            array('setting_name' => 'ads_top_banner_content', 'setting_value' => '<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXXXXXXXX" crossorigin="anonymous"></script>
<ins class="adsbygoogle"
     style="display:block"
     data-ad-client="ca-pub-XXXXXXXXXXXXXXXX"
     data-ad-slot="XXXXXXXXXX"
     data-ad-format="auto"
     data-full-width-responsive="true"></ins>
<script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script>'),

            // Sidebar Ad
            array('setting_name' => 'ads_sidebar_enabled', 'setting_value' => '1'),
            array('setting_name' => 'ads_sidebar_type', 'setting_value' => 'image'),
            array('setting_name' => 'ads_sidebar_content', 'setting_value' => 'https://via.placeholder.com/300x250/17A2B8/FFFFFF?text=%D8%A5%D8%B9%D9%84%D8%A7%D9%86+%D8%AC%D8%A7%D9%86%D8%A8%D9%8A'),
            array('setting_name' => 'ads_sidebar_url', 'setting_value' => 'https://example.com'),
            array('setting_name' => 'ads_sidebar_alt', 'setting_value' => 'إعلان جانبي'),

            // Bottom Banner Ad (إضافة ثالثة)
            array('setting_name' => 'ads_bottom_banner_enabled', 'setting_value' => '1'),
            array('setting_name' => 'ads_bottom_banner_type', 'setting_value' => 'html'),
            array('setting_name' => 'ads_bottom_banner_content', 'setting_value' => '<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 8px; margin: 20px 0;">
    <h3 style="margin: 0 0 10px 0; font-size: 1.5rem;">🎯 إعلان مخصص</h3>
    <p style="margin: 0 0 15px 0; font-size: 1.1rem;">هذا مثال على إعلان HTML مخصص مع تصميم جميل</p>
    <a href="https://example.com" style="background: white; color: #667eea; padding: 10px 20px; border-radius: 5px; text-decoration: none; font-weight: bold; display: inline-block;">اضغط هنا</a>
</div>')
        );

        foreach ($default_settings as $setting) {
            $wpdb->insert($table_name, $setting);
        }
    }
}
