<?php
/**
 * Auction Form Renderer - Unified form for create and edit
 *
 * @package AuctionSystem
 * @version 1.0.0
 * @description دالة موحدة لعرض فورم الإنشاء والتعديل
 */

if (!defined('ABSPATH')) {
    exit;
}

class AuctionFormRenderer {

    private static $instance = null;

    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Get texts based on mode
     */
    public function get_texts($mode = 'create') {
        $texts = array(
            'create' => array(
                'page_title' => __('إنشاء مزاد جديد', 'auction-system'),
                'page_description' => __('املأ النموذج التالي لإنشاء مزاد جديد. جميع الحقول المطلوبة مميزة بعلامة *', 'auction-system'),
                'submit_button' => __('إرسال للمراجعة', 'auction-system'),
                'draft_button' => __('حفظ كمسودة', 'auction-system'),
                'success_title' => __('تم إرسال المزاد بنجاح!', 'auction-system'),
                'success_message' => __('تم إرسال المزاد للمراجعة من قبل الإدارة. سيتم إشعارك عند الموافقة عليه وسيتم نشره على الموقع.', 'auction-system'),
                'create_another' => __('إنشاء مزاد آخر', 'auction-system'),
                'form_action' => 'create',
                'nonce_field' => 'create_auction_nonce'
            ),
            'edit' => array(
                'page_title' => __('تعديل المزاد', 'auction-system'),
                'page_description' => __('قم بتعديل معلومات المزاد. جميع الحقول المطلوبة مميزة بعلامة *', 'auction-system'),
                'submit_button' => __('حفظ التعديلات', 'auction-system'),
                'draft_button' => __('حفظ كمسودة', 'auction-system'),
                'success_title' => __('تم حفظ التعديلات بنجاح!', 'auction-system'),
                'success_message' => __('تم حفظ جميع التعديلات بنجاح. يمكنك مراجعة المزاد أو العودة للقائمة.', 'auction-system'),
                'create_another' => __('العودة للقائمة', 'auction-system'),
                'form_action' => 'edit',
                'nonce_field' => 'edit_auction_nonce'
            )
        );

        return isset($texts[$mode]) ? $texts[$mode] : $texts['create'];
    }

    /**
     * Render the complete auction form
     */
    public function render_form($mode = 'create', $auction_data = null, $error_message = null) {
        $texts = $this->get_texts($mode);
        $auction_settings = AuctionSettings::get_instance();

        // Get asset types for dropdown
        global $wpdb;
        $asset_types = $wpdb->get_results(
            "SELECT id, name FROM {$wpdb->prefix}auction_asset_types ORDER BY name ASC",
            ARRAY_A
        );

        ?>
        <div class="auction-container">
            <div class="container">
                <div class="page-header">
                    <h1><?php echo esc_html($texts['page_title']); ?></h1>
                    <p class="page-description">
                        <?php echo esc_html($texts['page_description']); ?>
                    </p>
                </div>

                <?php if ($error_message): ?>
                    <div class="auction-error-message">
                        <div class="error-content">
                            <strong><?php _e('خطأ:', 'auction-system'); ?></strong>
                            <?php echo esc_html($error_message); ?>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="auction-form-container">
                    <!-- Progress Steps -->
                    <div class="form-progress">
                        <div class="progress-steps">
                            <div class="step active" data-step="1">
                                <div class="step-number">1</div>
                                <div class="step-title"><?php _e('معلومات المزاد', 'auction-system'); ?></div>
                            </div>
                            <div class="step" data-step="2">
                                <div class="step-number">2</div>
                                <div class="step-title"><?php _e('معلومات الشركة', 'auction-system'); ?></div>
                            </div>
                            <div class="step" data-step="3">
                                <div class="step-number">3</div>
                                <div class="step-title"><?php _e('الأصول', 'auction-system'); ?></div>
                            </div>
                            <div class="step" data-step="4">
                                <div class="step-number">4</div>
                                <div class="step-title"><?php _e('البروشور', 'auction-system'); ?></div>
                            </div>
                            <div class="step" data-step="5">
                                <div class="step-number">5</div>
                                <div class="step-title"><?php _e('مراجعة وحفظ', 'auction-system'); ?></div>
                            </div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 20%;"></div>
                        </div>
                    </div>

                    <!-- Form -->
                    <form id="auction-form" method="post" enctype="multipart/form-data" novalidate>
                        <?php
                        wp_nonce_field($texts['nonce_field'], $texts['nonce_field']);

                        // Add hidden fields for edit mode
                        if ($mode === 'edit' && $auction_data) {
                            echo '<input type="hidden" name="auction_id" value="' . esc_attr($auction_data['id']) . '">';
                            echo '<input type="hidden" name="form_mode" value="edit">';
                        } else {
                            echo '<input type="hidden" name="form_mode" value="create">';
                        }
                        ?>

                        <!-- Step 1: Auction Details -->
                        <?php $this->render_step_1($mode, $auction_data); ?>

                        <!-- Step 2: Company Details -->
                        <?php $this->render_step_2($mode, $auction_data); ?>

                        <!-- Step 3: Assets -->
                        <?php $this->render_step_3($mode, $auction_data, $asset_types); ?>

                        <!-- Step 4: Brochure -->
                        <?php $this->render_step_4($mode, $auction_data); ?>

                        <!-- Step 5: Review -->
                        <?php $this->render_step_5($mode, $auction_data, $texts); ?>

                    </form>
                </div>
            </div>
        </div>

        <?php
        // Localize script
        $this->localize_script($mode, $auction_data, $auction_settings);
    }

    /**
     * Render success message
     */
    public function render_success($mode = 'create') {
        $texts = $this->get_texts($mode);
        ?>
        <div class="auction-container">
            <div class="container">
                <div class="auction-form-container">
                    <div class="auction-success-message">
                        <div class="success-content">
                            <div class="success-icon">
                                <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                    <polyline points="22,4 12,14.01 9,11.01"></polyline>
                                </svg>
                            </div>
                            <h2><?php echo esc_html($texts['success_title']); ?></h2>
                            <p><?php echo esc_html($texts['success_message']); ?></p>

                            <div class="success-actions">
                                <a href="<?php echo home_url(); ?>" class="btn btn-primary">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                        <polyline points="9,22 9,12 15,12 15,22"></polyline>
                                    </svg>
                                    <?php _e('العودة للرئيسية', 'auction-system'); ?>
                                </a>
                                <?php if ($mode === 'create'): ?>
                                    <a href="<?php echo get_permalink(); ?>" class="btn btn-secondary">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <line x1="12" y1="5" x2="12" y2="19"></line>
                                            <line x1="5" y1="12" x2="19" y2="12"></line>
                                        </svg>
                                        <?php echo esc_html($texts['create_another']); ?>
                                    </a>
                                <?php else: ?>
                                    <a href="<?php echo admin_url('edit.php?post_type=auction'); ?>" class="btn btn-secondary">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M19 12H5"></path>
                                            <polyline points="12,19 5,12 12,5"></polyline>
                                        </svg>
                                        <?php echo esc_html($texts['create_another']); ?>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render Step 1: Auction Details
     */
    private function render_step_1($mode, $auction_data) {
        $title = $mode === 'edit' && $auction_data ? $auction_data['title'] : '';
        $description = $mode === 'edit' && $auction_data ? $auction_data['description'] : '';
        $date = $mode === 'edit' && $auction_data ? $auction_data['date'] : '';
        $time = $mode === 'edit' && $auction_data ? $auction_data['time'] : '';
        $city = $mode === 'edit' && $auction_data ? $auction_data['city'] : '';
        $location = $mode === 'edit' && $auction_data ? $auction_data['location'] : '';
        $type = $mode === 'edit' && $auction_data ? $auction_data['type'] : '';
        ?>
        <div class="form-step active" data-step="1">
            <div class="step-content">
                <h3><?php _e('معلومات المزاد الأساسية', 'auction-system'); ?></h3>

                <div class="form-row">
                    <div class="form-group">
                        <label for="auction_title" class="required">
                            <?php _e('اسم المزاد', 'auction-system'); ?> *
                        </label>
                        <input type="text" id="auction_title" name="auction_title"
                               class="form-control" required
                               value="<?php echo esc_attr($title); ?>"
                               placeholder="<?php _e('مثال: مزاد نقوة الرياض', 'auction-system'); ?>">
                        <div class="error-message"></div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="auction_description">
                            <?php _e('وصف المزاد', 'auction-system'); ?>
                        </label>
                        <textarea id="auction_description" name="auction_description"
                                  class="form-control" rows="4"
                                  placeholder="<?php _e('وصف تفصيلي عن المزاد والأصول المعروضة...', 'auction-system'); ?>"><?php echo esc_textarea($description); ?></textarea>
                        <div class="error-message"></div>
                    </div>
                </div>

                <div class="form-row two-cols">
                    <div class="form-group">
                        <label for="auction_date" class="required">
                            <?php _e('تاريخ المزاد', 'auction-system'); ?> *
                        </label>
                        <input type="date" id="auction_date" name="auction_date"
                               class="form-control" required
                               value="<?php echo esc_attr($date); ?>">
                        <div class="error-message"></div>
                    </div>
                    <div class="form-group">
                        <label for="auction_time" class="required">
                            <?php _e('وقت المزاد', 'auction-system'); ?> *
                        </label>
                        <input type="time" id="auction_time" name="auction_time"
                               class="form-control" required
                               value="<?php echo esc_attr($time); ?>">
                        <div class="error-message"></div>
                    </div>
                </div>

                <div class="form-row two-cols">
                    <div class="form-group">
                        <label for="auction_city" class="required">
                            <?php _e('المدينة', 'auction-system'); ?> *
                        </label>
                        <input type="text" id="auction_city" name="auction_city"
                               class="form-control" required
                               value="<?php echo esc_attr($city); ?>"
                               placeholder="<?php _e('الرياض', 'auction-system'); ?>">
                        <div class="error-message"></div>
                    </div>
                    <div class="form-group">
                        <label for="auction_type" class="required">
                            <?php _e('نوع المزاد', 'auction-system'); ?> *
                        </label>
                        <select id="auction_type" name="auction_type" class="form-control" required>
                            <option value=""><?php _e('اختر نوع المزاد', 'auction-system'); ?></option>
                            <option value="in-person" <?php selected($type, 'in-person'); ?>><?php _e('مزاد حضوري', 'auction-system'); ?></option>
                            <option value="online" <?php selected($type, 'online'); ?>><?php _e('مزاد إلكتروني', 'auction-system'); ?></option>
                            <option value="hybrid" <?php selected($type, 'hybrid'); ?>><?php _e('مزاد مختلط', 'auction-system'); ?></option>
                        </select>
                        <div class="error-message"></div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="auction_location">
                            <?php _e('موقع المزاد على Google Maps', 'auction-system'); ?>
                        </label>
                        <input type="url" id="auction_location" name="auction_location"
                               class="form-control"
                               value="<?php echo esc_attr($location); ?>"
                               placeholder="https://maps.google.com/...">
                        <div class="error-message"></div>
                        <p class="description" style="margin-top: 8px; font-size: 14px; color: #666;">
                            <?php _e('انسخ رابط الموقع من Google Maps لعرض موقع المزاد على الخريطة', 'auction-system'); ?>
                        </p>
                        <div class="map-preview" id="auction-map-preview" style="display: none;">
                            <!-- Map will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-navigation">
                <button type="button" class="btn btn-primary next-step">
                    <?php _e('التالي', 'auction-system'); ?>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="9,18 15,12 9,6"></polyline>
                    </svg>
                </button>
            </div>
        </div>
        <?php
    }

    /**
     * Render Step 2: Company Details
     */
    private function render_step_2($mode, $auction_data) {
        $company_name = $mode === 'edit' && $auction_data ? $auction_data['company_name'] : '';
        $company_license = $mode === 'edit' && $auction_data ? $auction_data['company_license'] : '';
        $company_phone = $mode === 'edit' && $auction_data ? $auction_data['company_phone'] : '';
        $company_email = $mode === 'edit' && $auction_data ? $auction_data['company_email'] : '';
        $company_address = $mode === 'edit' && $auction_data ? $auction_data['company_address'] : '';
        $company_website = $mode === 'edit' && $auction_data ? $auction_data['company_website'] : '';
        ?>
        <div class="form-step" data-step="2">
            <div class="step-content">
                <h3><?php _e('معلومات الشركة المنظمة', 'auction-system'); ?></h3>

                <div class="form-row">
                    <div class="form-group">
                        <label for="company_name" class="required">
                            <?php _e('اسم الشركة', 'auction-system'); ?> *
                        </label>
                        <input type="text" id="company_name" name="company_name"
                               class="form-control" required
                               value="<?php echo esc_attr($company_name); ?>"
                               placeholder="<?php _e('مثال: شركة المزادات العقارية', 'auction-system'); ?>">
                        <div class="error-message"></div>
                    </div>
                </div>

                <div class="form-row two-cols">
                    <div class="form-group">
                        <label for="company_license" class="required">
                            <?php _e('رقم الترخيص', 'auction-system'); ?> *
                        </label>
                        <input type="text" id="company_license" name="company_license"
                               class="form-control" required
                               value="<?php echo esc_attr($company_license); ?>"
                               placeholder="<?php _e('مثال: *********', 'auction-system'); ?>">
                        <div class="error-message"></div>
                    </div>
                    <div class="form-group">
                        <label for="company_phone" class="required">
                            <?php _e('رقم الهاتف', 'auction-system'); ?> *
                        </label>
                        <input type="tel" id="company_phone" name="company_phone"
                               class="form-control" required
                               value="<?php echo esc_attr($company_phone); ?>"
                               placeholder="<?php _e('مثال: +966501234567', 'auction-system'); ?>">
                        <div class="error-message"></div>
                    </div>
                </div>

                <div class="form-row two-cols">
                    <div class="form-group">
                        <label for="company_email" class="required">
                            <?php _e('البريد الإلكتروني', 'auction-system'); ?> *
                        </label>
                        <input type="email" id="company_email" name="company_email"
                               class="form-control" required
                               value="<?php echo esc_attr($company_email); ?>"
                               placeholder="<?php _e('مثال: <EMAIL>', 'auction-system'); ?>">
                        <div class="error-message"></div>
                    </div>
                    <div class="form-group">
                        <label for="company_website">
                            <?php _e('الموقع الإلكتروني', 'auction-system'); ?>
                        </label>
                        <input type="url" id="company_website" name="company_website"
                               class="form-control"
                               value="<?php echo esc_attr($company_website); ?>"
                               placeholder="<?php _e('مثال: https://www.company.com', 'auction-system'); ?>">
                        <div class="error-message"></div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="company_address" class="required">
                            <?php _e('عنوان الشركة', 'auction-system'); ?> *
                        </label>
                        <textarea id="company_address" name="company_address"
                                  class="form-control" rows="3" required
                                  placeholder="<?php _e('العنوان الكامل للشركة...', 'auction-system'); ?>"><?php echo esc_textarea($company_address); ?></textarea>
                        <div class="error-message"></div>
                    </div>
                </div>
            </div>

            <div class="form-navigation">
                <button type="button" class="btn btn-secondary prev-step">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="15,18 9,12 15,6"></polyline>
                    </svg>
                    <?php _e('السابق', 'auction-system'); ?>
                </button>
                <button type="button" class="btn btn-primary next-step">
                    <?php _e('التالي', 'auction-system'); ?>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="9,18 15,12 9,6"></polyline>
                    </svg>
                </button>
            </div>
        </div>
        <?php
    }

    /**
     * Render Step 3: Assets (simplified for now)
     */
    private function render_step_3($mode, $auction_data, $asset_types) {
        ?>
        <div class="form-step" data-step="3">
            <div class="step-content">
                <h3><?php _e('الأصول المعروضة', 'auction-system'); ?></h3>
                <div id="assets-container">
                    <!-- Assets will be loaded via JavaScript -->
                </div>
                <button type="button" class="btn btn-outline add-asset">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    <?php _e('إضافة أصل', 'auction-system'); ?>
                </button>
            </div>
            <div class="form-navigation">
                <button type="button" class="btn btn-secondary prev-step">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="15,18 9,12 15,6"></polyline>
                    </svg>
                    <?php _e('السابق', 'auction-system'); ?>
                </button>
                <button type="button" class="btn btn-primary next-step">
                    <?php _e('التالي', 'auction-system'); ?>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="9,18 15,12 9,6"></polyline>
                    </svg>
                </button>
            </div>
        </div>
        <?php
    }

    /**
     * Render Step 4: Brochure
     */
    private function render_step_4($mode, $auction_data) {
        $existing_brochure = '';
        if ($mode === 'edit' && isset($auction_data['brochure_url']) && !empty($auction_data['brochure_url'])) {
            $existing_brochure = $auction_data['brochure_url'];
        }
        ?>
        <div class="form-step" data-step="4">
            <div class="step-content">
                <h3><?php _e('البروشور', 'auction-system'); ?></h3>

                <?php if (!empty($existing_brochure)): ?>
                    <div class="existing-brochure" style="margin-bottom: 20px;">
                        <h4 style="margin-bottom: 10px;"><?php _e('البروشور الحالي:', 'auction-system'); ?></h4>
                        <div class="brochure-preview" style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; background: #f9f9f9;">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14,2 14,8 20,8"></polyline>
                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                    <polyline points="10,9 9,9 8,9"></polyline>
                                </svg>
                                <div>
                                    <div style="font-weight: bold; color: #333;">
                                        <?php echo basename($existing_brochure); ?>
                                    </div>
                                    <div style="font-size: 12px; color: #666;">
                                        <?php _e('ملف PDF', 'auction-system'); ?>
                                    </div>
                                </div>
                                <div style="margin-left: auto;">
                                    <a href="<?php echo esc_url($existing_brochure); ?>" target="_blank"
                                       class="btn btn-sm btn-outline" style="margin-right: 10px;">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                            <circle cx="12" cy="12" r="3"></circle>
                                        </svg>
                                        <?php _e('عرض', 'auction-system'); ?>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" onclick="AuctionSystem.removeBrochure()">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <polyline points="3,6 5,6 21,6"></polyline>
                                            <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                                        </svg>
                                        <?php _e('حذف', 'auction-system'); ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="existing_brochure_url" value="<?php echo esc_attr($existing_brochure); ?>">
                    </div>
                <?php endif; ?>

                <div class="brochure-upload">
                    <div class="file-upload-area" id="brochure-upload-area">
                        <div class="upload-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                <polyline points="14,2 14,8 20,8"></polyline>
                                <line x1="16" y1="13" x2="8" y2="13"></line>
                                <line x1="16" y1="17" x2="8" y2="17"></line>
                                <polyline points="10,9 9,9 8,9"></polyline>
                            </svg>
                        </div>
                        <div class="upload-text">
                            <?php if (!empty($existing_brochure)): ?>
                                <?php _e('اضغط لاختيار بروشور جديد أو اسحبه هنا', 'auction-system'); ?>
                            <?php else: ?>
                                <?php _e('اضغط لاختيار البروشور أو اسحبه هنا', 'auction-system'); ?>
                            <?php endif; ?>
                        </div>
                        <div class="upload-hint">
                            <?php _e('ملفات PDF, DOC, DOCX - الحد الأقصى 10 ميجابايت', 'auction-system'); ?>
                        </div>
                    </div>

                    <input type="file" id="brochure_file" name="brochure_file"
                           accept=".pdf,.doc,.docx" style="display: none;">

                    <div id="brochure-preview" style="display: none; margin-top: 15px;">
                        <!-- New brochure preview will be shown here -->
                    </div>
                </div>
            </div>
            <div class="form-navigation">
                <button type="button" class="btn btn-secondary prev-step">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="15,18 9,12 15,6"></polyline>
                    </svg>
                    <?php _e('السابق', 'auction-system'); ?>
                </button>
                <button type="button" class="btn btn-primary next-step">
                    <?php _e('التالي', 'auction-system'); ?>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="9,18 15,12 9,6"></polyline>
                    </svg>
                </button>
            </div>
        </div>
        <?php
    }

    /**
     * Render Step 5: Review and Submit
     */
    private function render_step_5($mode, $auction_data, $texts) {
        ?>
        <div class="form-step" data-step="5">
            <div class="step-content">
                <h3><?php _e('مراجعة وحفظ', 'auction-system'); ?></h3>
                <div class="review-sections">
                    <div class="review-section">
                        <h4><?php _e('معلومات المزاد', 'auction-system'); ?></h4>
                        <div id="review-auction-details"></div>
                    </div>
                    <div class="review-section">
                        <h4><?php _e('معلومات الشركة', 'auction-system'); ?></h4>
                        <div id="review-company-details"></div>
                    </div>
                </div>

                <div class="terms-section">
                    <label class="checkbox-container">
                        <input type="checkbox" id="accept_terms" name="accept_terms" required>
                        <span class="checkmark"></span>
                        <?php _e('أوافق على الشروط والأحكام وأؤكد صحة جميع البيانات المدخلة', 'auction-system'); ?> *
                    </label>
                    <div class="error-message"></div>
                </div>
            </div>

            <div class="form-navigation">
                <button type="button" class="btn btn-secondary prev-step">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="15,18 9,12 15,6"></polyline>
                    </svg>
                    <?php _e('السابق', 'auction-system'); ?>
                </button>

                <div class="submit-buttons">
                    <?php if (is_user_logged_in()): ?>
                        <button type="submit" name="save_as_draft" class="btn btn-secondary">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                                <polyline points="17,21 17,13 7,13 7,21"></polyline>
                                <polyline points="7,3 7,8 15,8"></polyline>
                            </svg>
                            <?php echo esc_html($texts['draft_button']); ?>
                        </button>
                    <?php endif; ?>

                    <button type="submit" name="submit_for_review" class="btn btn-success">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="20,6 9,17 4,12"></polyline>
                        </svg>
                        <?php echo esc_html($texts['submit_button']); ?>
                    </button>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Localize script with data
     */
    private function localize_script($mode, $auction_data, $auction_settings) {
        // Prepare assets data for JavaScript
        $assets_data = array();
        if ($mode === 'edit' && isset($auction_data['assets']) && is_array($auction_data['assets'])) {
            foreach ($auction_data['assets'] as $index => $asset) {
                $assets_data[] = array(
                    'index' => $index,
                    'title' => $asset['title'],
                    'asset_type_id' => $asset['asset_type_id'],
                    'asset_type_name' => $asset['asset_type_name'],
                    'description' => $asset['description'],
                    'area' => $asset['area'],
                    'city' => $asset['city'],
                    'district' => $asset['district'],
                    'map_link' => $asset['map_link'],
                    'coordinates' => $asset['coordinates'],
                    'deed_number' => $asset['deed_number'],
                    'starting_price' => $asset['starting_price'],
                    'minimum_bid' => $asset['minimum_bid'],
                    'images' => $asset['images']
                );
            }
        }

        wp_localize_script('auction-create-form', 'auctionAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('auction_ajax_nonce'),
            'mode' => $mode,
            'auction_data' => $auction_data,
            'existing_assets' => $assets_data,
            'maxImages' => $auction_settings->get_setting('max_images_per_asset', '10'),
            'maxImageSize' => $auction_settings->get_setting('max_image_size_mb', '5'),
            'maxBrochureSize' => $auction_settings->get_setting('max_brochure_size_mb', '10'),
            'strings' => array(
                'required_field' => __('هذا الحقل مطلوب', 'auction-system'),
                'invalid_email' => __('البريد الإلكتروني غير صحيح', 'auction-system'),
                'invalid_url' => __('الرابط غير صحيح', 'auction-system'),
                'file_too_large' => __('حجم الملف كبير جداً', 'auction-system'),
                'invalid_file_type' => __('نوع الملف غير مدعوم', 'auction-system'),
                'confirm_delete' => __('هل أنت متأكد من الحذف؟', 'auction-system'),
                'loading' => __('جاري التحميل...', 'auction-system'),
                'error' => __('حدث خطأ', 'auction-system')
            )
        ));
    }
}
