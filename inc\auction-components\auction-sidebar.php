<?php
/**
 * Auction Sidebar Component
 *
 * @package AuctionSystem
 * @version 1.0.0
 * @description مكون الشريط الجانبي للمزاد
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Include required component files
require_once get_stylesheet_directory() . '/inc/auction-components/company-info.php';
require_once get_stylesheet_directory() . '/inc/auction-components/contact-card.php';

/**
 * Display complete auction sidebar
 */
if (!function_exists('auction_display_sidebar')) {
    function auction_display_sidebar($auction_details, $auction_data) {
        ?>
        <!-- Sidebar -->
        <div class="sidebar">
        <?php auction_display_sidebar_top_ad($auction_data); ?>
        <?php auction_display_company_info($auction_details); ?>
        <?php auction_display_contact_card($auction_details); ?>
            <?php auction_display_sidebar_bottom_ad($auction_data); ?>

        </div>
        <?php
    }
}
